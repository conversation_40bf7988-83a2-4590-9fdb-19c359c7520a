// This is a temporary file to hold the new shader code
// We'll use this to create a properly formatted string for the glassFsSource getter

const shaderCode = `precision mediump float;

uniform sampler2D u_backgroundTexture;
uniform vec2 u_resolution;
uniform vec2 u_glassSize;
uniform float u_cornerRadius;
uniform float u_ior;
uniform float u_glassThickness;
uniform float u_normalStrength;
uniform float u_displacementScale;
uniform float u_heightTransitionWidth; // Renamed from u_heightBlurFactor for clarity
uniform float u_sminSmoothing;     // New: SDF smoothing factor k
uniform int u_showNormals;
uniform float u_blurRadius;        // New: Blur radius for frosted glass effect
uniform vec4 u_overlayColor;       // New: Overlay color for the glass (e.g., subtle white)
uniform float u_highlightWidth;    // New: Width of the white highlight at the edge

varying vec2 v_screenTexCoord;
varying vec2 v_shapeCoord;

// Polynomial smooth min (quartic)
// k controls the smoothness/radius of the blend
float smin_polynomial(float a, float b, float k) {
    if (k <= 0.0) return min(a, b); // Avoid division by zero or no smoothing
    float h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0);
    return mix(b, a, h) - k * h * (1.0 - h);
}

// Polynomial smooth max
float smax_polynomial(float a, float b, float k) {
    if (k <= 0.0) return max(a, b);
    // return -smin_polynomial(-a, -b, k); // Alternative formulation
    float h = clamp(0.5 + 0.5 * (a - b) / k, 0.0, 1.0);
    return mix(b, a, h) + k * h * (1.0 - h); // Note: +k and (a-b)
}

// Original sdRoundedBox (for reference or if k_smooth is 0)
float sdRoundedBoxSharp(vec2 p, vec2 b, float r) {
    vec2 q = abs(p) - b + r;
    return min(max(q.x, q.y), 0.0) + length(max(q, 0.0)) - r;
}

// Smoothed sdRoundedBox using polynomial smin/smax
float sdRoundedBoxSmooth(vec2 p, vec2 b, float r, float k_smooth) {
    if (k_smooth <= 0.0) { // Fallback to sharp if no smoothing
        return sdRoundedBoxSharp(p,b,r);
    }
    vec2 q = abs(p) - b + r;

    // Term A: max(q.x, q.y) - This is a key part for corner definition
    float termA_smooth = smax_polynomial(q.x, q.y, k_smooth);

    // Term B: min(termA_smooth, 0.0) - Clamps the distance for points along straight edges
    // Smoothing this min( , 0.0) can be tricky. Using a smaller k or no smoothing might be safer.
    // Let's try with a potentially smaller k for this specific part.
    float termB_smooth = smin_polynomial(termA_smooth, 0.0, k_smooth * 0.5);

    // Term C: length(max(q, 0.0)) - Distance from corner center for points in corner region
    // max(q, 0.0) is vec2(max(q.x, 0.0), max(q.y, 0.0))
    vec2 q_for_length_smooth = vec2(
        smax_polynomial(q.x, 0.0, k_smooth),
        smax_polynomial(q.y, 0.0, k_smooth)
    );
    float termC_smooth = length(q_for_length_smooth);

    return termB_smooth + termC_smooth - r;
}

// Helper function to convert SDF to height
float getHeightFromSDF(vec2 p_pixel_space, vec2 b_pixel_space, float r_pixel, float k_s, float transition_w) {
    float dist_sample = sdRoundedBoxSmooth(p_pixel_space, b_pixel_space, r_pixel, k_s);
    // Normalize dist_sample to [-1, 1] within the transition band
    float normalized_dist = dist_sample / transition_w;

    // Use a logistic sigmoid function for a steep drop at the edge (normalized_dist=0) and flatten out
    // A higher steepness_factor leads to a sharper transition
    const float steepness_factor = 6.0; // This value can be tuned
    float height = 1.0 - (1.0 / (1.0 + exp(-normalized_dist * steepness_factor)));

    // Clamp to [0, 1] to ensure it stays within valid height range
    return clamp(height, 0.0, 1.0);
}

void main() {
    float actualCornerRadius = min(u_cornerRadius, min(u_glassSize.x, u_glassSize.y) / 2.0);

    // Current point in pixel space relative to glass center
    vec2 current_p_pixel = v_shapeCoord * u_glassSize;
    vec2 glass_half_size_pixel = u_glassSize / 2.0;

    // Initial SDF check for discard (can use sharp version for efficiency if k_smooth is large)
    float dist_for_shape_boundary = sdRoundedBoxSmooth(current_p_pixel, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing);
    if (dist_for_shape_boundary > 0.001) { // Discard if clearly outside transition band
        discard;
    }


    vec2 pixel_step_in_norm_space = vec2(1.0 / u_glassSize.x, 1.0 / u_glassSize.y); // Step in v_shapeCoord's space

    // Sampling steps in normalized shape space (v_shapeCoord space)
    float norm_step_x1 = pixel_step_in_norm_space.x * 0.75;
    float norm_step_y1 = pixel_step_in_norm_space.y * 0.75;
    float norm_step_x2 = pixel_step_in_norm_space.x * 1.5;
    float norm_step_y2 = pixel_step_in_norm_space.y * 1.5;

    // Calculate X direction gradient
    // getHeightFromSDF expects pixel space coords for p, b, r, k_s, transition_w
    float h_px1 = getHeightFromSDF((v_shapeCoord + vec2(norm_step_x1, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_nx1 = getHeightFromSDF((v_shapeCoord - vec2(norm_step_x1, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_px2 = getHeightFromSDF((v_shapeCoord + vec2(norm_step_x2, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_nx2 = getHeightFromSDF((v_shapeCoord - vec2(norm_step_x2, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);

    // Denominators are distances in pixels
    float grad_x1 = (h_px1 - h_nx1) / (2.0 * norm_step_x1 * u_glassSize.x);
    float grad_x2 = (h_px2 - h_nx2) / (2.0 * norm_step_x2 * u_glassSize.x);
    float delta_x = mix(grad_x1, grad_x2, 0.5);

    // Calculate Y direction gradient
    float h_py1 = getHeightFromSDF((v_shapeCoord + vec2(0.0, norm_step_y1)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_ny1 = getHeightFromSDF((v_shapeCoord - vec2(0.0, norm_step_y1)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_py2 = getHeightFromSDF((v_shapeCoord + vec2(0.0, norm_step_y2)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_ny2 = getHeightFromSDF((v_shapeCoord - vec2(0.0, norm_step_y2)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);

    float grad_y1 = (h_py1 - h_ny1) / (2.0 * norm_step_y1 * u_glassSize.y);
    float grad_y2 = (h_py2 - h_ny2) / (2.0 * norm_step_y2 * u_glassSize.y);
    float delta_y = mix(grad_y1, grad_y2, 0.5);

    vec3 surfaceNormal3D = normalize(vec3(-delta_x * u_normalStrength, -delta_y * u_normalStrength, 1.0));

    if (u_showNormals == 1) {
        gl_FragColor = vec4(surfaceNormal3D * 0.5 + 0.5, 1.0); // Remap from [-1,1] to [0,1] for color
        return;
    }

    vec3 incidentLightDir = normalize(vec3(0.0, 0.0, -1.0));
    vec3 refractedIntoGlass = refract(incidentLightDir, surfaceNormal3D, 1.0 / u_ior);
    vec3 refractedOutOfGlass = refract(refractedIntoGlass, -surfaceNormal3D, u_ior);

    vec2 offset_in_pixels = refractedOutOfGlass.xy * u_glassThickness;
    vec2 offset = (offset_in_pixels / u_resolution) * u_displacementScale;

    vec2 refractedTexCoord = v_screenTexCoord + offset;
    refractedTexCoord = clamp(refractedTexCoord, 0.001, 0.999);

    // Frosted Glass Effect: Apply a 3x3 box blur to the refracted texture
    vec4 blurredColor = vec4(0.0);
    vec2 texelSize = 1.0 / u_resolution; // Size of one pixel in texture coordinates
    float blurPixelRadius = u_blurRadius;

    // Unrolled 3x3 blur samples
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0,  0.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0,  0.0) * blurPixelRadius * texelSize); // Center sample
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0,  0.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0,  1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0,  1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0,  1.0) * blurPixelRadius * texelSize);

    blurredColor /= 9.0; // Divide by total number of samples (3x3 = 9)

    // Mix with an overlay color to make the glass stand out more
    // The height value here can be used as an alpha or blending factor if desired
    // For a subtle overlay, we can just mix with a fixed alpha
    float height_val = getHeightFromSDF(current_p_pixel, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    vec4 finalColor = mix(blurredColor, u_overlayColor, height_val * 0.15); // Adjust 0.15 for desired opacity

    // Apply highlight on top of the final color
    float highlight_dist = abs(dist_for_shape_boundary);
    // The highlight will be strongest at highlight_dist = 0.0 and fade out towards u_highlightWidth
    float highlight_alpha = 1.0 - smoothstep(0.0, u_highlightWidth, highlight_dist);
    highlight_alpha = max(0.0, highlight_alpha); // Ensure it's not negative

    // Directional highlight based on normal
    // We want highlight stronger when surfaceNormal3D.x and surfaceNormal3D.y have the same sign
    // This corresponds to normals pointing towards top-left (-x, -y) or bottom-right (+x, +y) edges
    float directionalFactor = (surfaceNormal3D.x * surfaceNormal3D.y + 1.0) * 0.5; // Scales from 0 to 1
    // You can add a boost to this factor if the highlight is too subtle
    // directionalFactor = pow(directionalFactor, 0.5); // Example: apply power for non-linear control

    float finalHighlightAlpha = highlight_alpha * directionalFactor;

    gl_FragColor = mix(finalColor, vec4(1.0, 1.0, 1.0, 1.0), finalHighlightAlpha);
}`;

// Now we need to escape all backticks in the shader code
const escapedShaderCode = shaderCode.replace(/`/g, '\\`');

console.log(`get glassFsSource() { return \`${escapedShaderCode}\`; }`);
