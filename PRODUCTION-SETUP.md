# Minewache Website Production Setup Guide

This guide provides step-by-step instructions for making the Minewache website production-ready.

## Production Environment Specifications

The Minewache website is hosted on a VPS (Virtual Private Server) in Ohio, USA, sponsored by Bisect-Hosting. The production environment has the following specifications:

- **Operating System**: Ubuntu Server 24.0.1
- **Web Server**: Nginx with PHP 8.3 FPM
- **Database**: MySQL with secure-server-installation
- **Cache Layer**: Redis
- **Domain**: minewache.de with SSL/TLS encryption
- **SSL/TLS**: Cloudflare wildcard certificate for all subdomains
- **Ports**: 8080, 80, and 443 (HTTP redirects to HTTPS)
- **Additional Software**: Composer, NPM, Node.js

For detailed configuration files and setup instructions specific to this environment, see [Ubuntu Production Configuration](ubuntu-production-config.md).

## Prerequisites

- PHP 8.3 or higher
- Composer
- Node.js and npm
- MySQL/MariaDB
- FFmpeg (for media processing)
- Redis (required for production)

## Production Setup Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/minewache-website.git
cd minewache-website
```

### 2. Environment Configuration

Copy the example environment file and generate an application key:

```bash
cp .env.example .env
php artisan key:generate
```

Edit the `.env` file to configure production settings:

- Set `APP_ENV=production`
- Set `APP_DEBUG=false`
- Set `APP_URL` to your production URL
- Configure database connection
- Configure Redis for cache, session, and queue
- Configure Discord bot integration
- Configure Reverb WebSocket server
- Configure FFmpeg paths

### 3. Install Dependencies

Install PHP dependencies:

```bash
composer install --no-dev --optimize-autoloader
```

Install JavaScript dependencies:

```bash
npm ci
```

### 4. Build Frontend Assets

Compile assets for production:

```bash
npm run build
```

### 5. Database Setup

Run database migrations:

```bash
php artisan migrate --force
```

### 6. Cache Configuration

Cache configuration files for better performance:

```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 7. Set Up Storage

Create a symbolic link for storage:

```bash
php artisan storage:link
```

### 8. Set Proper Permissions

Ensure proper permissions for storage and bootstrap/cache directories:

```bash
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### 9. Set Up Queue Worker

For Linux/Unix systems, set up a systemd service for the queue worker:

```bash
sudo bash scripts/setup-production.sh
```

For Windows systems, run the deployment script:

```powershell
.\deploy.ps1
```

### 10. Set Up Reverb WebSocket Server

For Linux/Unix systems, the `setup-production.sh` script will set up a systemd service for the Reverb WebSocket server.

For Windows systems, the `deploy.ps1` script will start the Reverb WebSocket server.

### 11. Set Up Discord Bot

For Linux/Unix systems, the `setup-production.sh` script will set up a systemd service for the Discord bot.

For Windows systems, the `deploy.ps1` script will start the Discord bot.

### 12. Test Production Setup

Run the production testing script to ensure everything is working correctly:

```powershell
# For Windows
.\test-production.ps1

# For Linux/Unix
# Coming soon
```

## Automated Deployment

### Windows Deployment

To deploy the website on Windows, run:

```powershell
.\deploy.ps1
```

This script will:
- Create a backup of the current installation
- Pull the latest changes from git
- Install dependencies
- Build frontend assets
- Run database migrations
- Cache configuration
- Deploy the Discord bot
- Start all services

### Linux/Unix Deployment

To deploy the website on Linux/Unix, run:

```bash
sudo bash scripts/setup-production.sh
```

This script will:
- Set up systemd services for all components
- Configure log rotation
- Set proper permissions

## Production Checklist

- [ ] Environment is set to production (`APP_ENV=production` in .env)
- [ ] Debug mode is disabled (`APP_DEBUG=false` in .env)
- [ ] Application key is set (`php artisan key:generate`)
- [ ] Database is properly configured (MySQL with secure settings)
- [ ] Redis is properly configured for cache, session, and queue
- [ ] Frontend assets are compiled (`npm run build`)
- [ ] Configuration is cached (`php artisan config:cache`)
- [ ] Routes are cached (`php artisan route:cache`)
- [ ] Views are cached (`php artisan view:cache`)
- [ ] Queue worker is running (`systemctl status minewache-queue.service`)
- [ ] Reverb WebSocket server is running (`systemctl status minewache-reverb.service`)
- [ ] Discord bot is running (`systemctl status minewache-discord.service`)
- [ ] Storage link is created (`php artisan storage:link`)
- [ ] Proper permissions are set (www-data:www-data)
- [ ] Nginx is configured with SSL/TLS (`nginx -t` to verify)
- [ ] Ports 80, 443, and 8080 are open and properly configured
- [ ] Cloudflare wildcard certificate is installed and valid
- [ ] Automatic certificate renewal is configured
- [ ] Firewall is configured to allow necessary traffic
- [ ] Backups are configured and working

## Troubleshooting

### Common Issues

1. **Web server returns 500 error**
   - Check the Laravel log file at `storage/logs/laravel.log`
   - Ensure proper permissions on storage and bootstrap/cache directories
   - Verify that the .env file exists and is properly configured

2. **Queue worker is not processing jobs**
   - Check if the queue worker service is running
   - Verify Redis connection if using Redis for queues
   - Check the queue worker log file

3. **Reverb WebSocket server is not working**
   - Check if the Reverb service is running
   - Verify that the Reverb configuration in .env is correct
   - Check the Reverb log file

4. **Discord bot is not connecting**
   - Verify that the Discord bot token is correct
   - Check if the Discord bot service is running
   - Check the Discord bot log file

### Log Files

- Laravel log: `storage/logs/laravel.log`
- Queue worker log: `/var/log/minewache-queue.log` (Linux/Unix)
- Reverb log: `/var/log/minewache-reverb.log` (Linux/Unix)
- Discord bot log: `/var/log/minewache-discord.log` (Linux/Unix)
- Deployment log: `deploy.log`
- Production test log: `test-production.log`

## Security Considerations

- Ensure that `APP_DEBUG` is set to `false` in production
- Use HTTPS for all production traffic
- Set secure cookie settings in .env
- Use strong passwords for database and Redis
- Restrict access to the server
- Regularly update dependencies
- Set up proper firewall rules
- Configure proper CORS settings
- Use proper session and cache drivers
- Set up proper error handling and logging
