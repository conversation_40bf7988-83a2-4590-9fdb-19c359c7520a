<?php

namespace App\Livewire;

use App\Models\Ticket;
use App\Services\DiscordService;
use Livewire\Component;

class TicketCreate extends Component
{
    public $title = '';
    public $description = '';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string',
    ];

    public function render()
    {
        return view('livewire.ticket-create');
    }

    public function submit()
    {
        // Ticket system is temporarily disabled
        session()->flash('info', 'The ticket system is temporarily disabled for maintenance.');
        return redirect()->route('home');

        // Original code commented out while ticket system is disabled
        /*
        $this->validate();

        $ticket = new Ticket([
            'user_id' => auth()->id(),
            'title' => $this->title,
            'description' => $this->description,
            'status' => 'open',
        ]);

        $ticket->save();

        // Create a Discord channel for this ticket if Discord integration is enabled
        if (config('services.discord.enabled', false)) {
            $discordService = app(DiscordService::class);
            $channelId = $discordService->createTicketChannel($ticket);

            if ($channelId) {
                $ticket->discord_channel_id = $channelId;
                $ticket->save();
            }
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.created_successfully'));
        */
    }
}
