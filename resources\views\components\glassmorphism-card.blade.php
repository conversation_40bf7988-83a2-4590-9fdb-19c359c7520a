@props([
    'level' => 'default', // default, light, dark
    'border' => null, // null, primary, secondary, accent
    'padding' => 'p-6 md:p-8', // custom padding classes
    'rounded' => 'rounded-xl', // custom border radius
    'shadow' => 'shadow-xl', // custom shadow
    'hover' => false, // enable hover effect
])

@php
    $baseClasses = 'glassmorphism glassmorphism-target backdrop-blur-md'; // Added glassmorphism-target

    // Border color
    $borderClasses = match($border) {
        'primary' => 'border-l-4 border-primary',
        'secondary' => 'border-l-4 border-secondary',
        'accent' => 'border-l-4 border-accent',
        'primary-all' => 'border-2 border-primary',
        'secondary-all' => 'border-2 border-secondary',
        'accent-all' => 'border-2 border-accent',
        default => 'border border-gray-200 dark:border-gray-700',
    };

    // Hover effect
    $hoverClasses = $hover ? 'transition-all duration-300 hover:shadow-2xl hover:-translate-y-1' : '';

    // $bgClasses has been removed from this concatenation
    $classes = $baseClasses . ' ' . $borderClasses . ' ' . $padding . ' ' . $rounded . ' ' . $shadow . ' ' . $hoverClasses;
@endphp

<div {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</div>
