# Minewache Website - Ubuntu 24.0.1 Production Configuration

This document provides configuration files and instructions for setting up the Minewache website on Ubuntu Server 24.0.1 with Nginx, PHP 8.3 FPM, MySQL, Redis, and Cloudflare SSL/TLS.

## Table of Contents

1. [Nginx Configuration](#nginx-configuration)
2. [PHP-FPM Configuration](#php-fpm-configuration)
3. [MySQL Configuration](#mysql-configuration)
4. [Redis Configuration](#redis-configuration)
5. [SSL/TLS with Cloudflare](#ssltls-with-cloudflare)
6. [Service Management](#service-management)
7. [Deployment Script](#deployment-script)
8. [Monitoring](#monitoring)
9. [Cleanup](#cleanup)

## Nginx Configuration

Create a new Nginx configuration file for the Minewache website:

```bash
sudo nano /etc/nginx/sites-available/minewache.de
```

Add the following configuration:

```nginx
server {
    # Listen on port 80 (HTTP) and redirect to HTTPS
    listen 80;
    listen [::]:80;
    server_name minewache.de *.minewache.de;

    # Redirect all HTTP requests to HTTPS
    return 301 https://$host$request_uri;
}

server {
    # Listen on port 443 (HTTPS) and 8080
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    listen 8080 ssl http2;
    listen [::]:8080 ssl http2;

    server_name minewache.de *.minewache.de;
    root /var/www/minewache-website/public;

    # SSL/TLS Configuration
    ssl_certificate /etc/letsencrypt/live/minewache.de/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/minewache.de/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/minewache.de/chain.pem;

    # SSL/TLS Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' wss://*.minewache.de; frame-src 'self' https://www.youtube.com";

    # Logging
    access_log /var/log/nginx/minewache.de.access.log;
    error_log /var/log/nginx/minewache.de.error.log;

    # Index file
    index index.php;

    # Character encoding
    charset utf-8;

    # Handle requests
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle PHP files
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 600;
    }

    # WebSocket proxy for Laravel Reverb
    location /app {
        proxy_pass http://127.0.0.1:6001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }

    # Deny access to hidden files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Favicon and robots.txt
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    # Error pages
    error_page 404 /index.php;
}
```

Enable the configuration:

```bash
sudo ln -s /etc/nginx/sites-available/minewache.de /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## PHP-FPM Configuration

Install PHP 8.3 and required extensions:

```bash
sudo apt update
sudo apt install -y software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.3-fpm php8.3-cli php8.3-common php8.3-mysql php8.3-zip php8.3-gd php8.3-mbstring php8.3-curl php8.3-xml php8.3-bcmath php8.3-intl php8.3-redis
```

Configure PHP-FPM:

```bash
sudo nano /etc/php/8.3/fpm/php.ini
```

Update the following settings:

```ini
; Maximum execution time
max_execution_time = 120

; Maximum input time
max_input_time = 120

; Memory limit
memory_limit = 256M

; Error reporting
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php/php_errors.log

; File uploads
upload_max_filesize = 64M
post_max_size = 64M

; OPcache settings
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
opcache.enable_cli=1

; Session settings
session.save_handler = redis
session.save_path = "tcp://127.0.0.1:6379"
```

Create a custom PHP-FPM pool configuration:

```bash
sudo nano /etc/php/8.3/fpm/pool.d/minewache.conf
```

Add the following configuration:

```ini
[minewache]
user = www-data
group = www-data
listen = /var/run/php/php8.3-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 25
pm.start_servers = 5
pm.min_spare_servers = 2
pm.max_spare_servers = 10
pm.max_requests = 500

php_admin_value[error_log] = /var/log/php/minewache_error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 256M
php_admin_value[upload_max_filesize] = 64M
php_admin_value[post_max_size] = 64M
php_admin_value[max_execution_time] = 120
php_admin_value[max_input_time] = 120
```

Create the PHP log directory:

```bash
sudo mkdir -p /var/log/php
sudo chown www-data:www-data /var/log/php
```

Restart PHP-FPM:

```bash
sudo systemctl restart php8.3-fpm
```

## MySQL Configuration

Install MySQL:

```bash
sudo apt install -y mysql-server
```

Secure MySQL installation:

```bash
sudo mysql_secure_installation
```

Create a MySQL configuration file:

```bash
sudo nano /etc/mysql/conf.d/minewache.cnf
```

Add the following configuration:

```ini
[mysqld]
# Basic Settings
user            = mysql
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
port            = 3306
basedir         = /usr
datadir         = /var/lib/mysql
tmpdir          = /tmp
lc-messages-dir = /usr/share/mysql

# Security Settings
local-infile=0
symbolic-links=0

# InnoDB Settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 1
innodb_flush_method = O_DIRECT

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Connection Settings
max_connections = 100
max_allowed_packet = 16M
thread_cache_size = 8

# Query Cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 1M

# Logging
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 2
```

Create the database and user:

```bash
sudo mysql -e "CREATE DATABASE minewache CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER 'minewache_user'@'localhost' IDENTIFIED BY 'STRONG_PASSWORD_HERE';"
sudo mysql -e "GRANT ALL PRIVILEGES ON minewache.* TO 'minewache_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

Restart MySQL:

```bash
sudo systemctl restart mysql
```

## Redis Configuration

Install Redis:

```bash
sudo apt install -y redis-server
```

Configure Redis:

```bash
sudo nano /etc/redis/redis.conf
```

Update the following settings:

```
# Basic Settings
port 6379
bind 127.0.0.1
protected-mode yes
daemonize yes
supervised systemd

# Performance Settings
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis

# Security
requirepass STRONG_REDIS_PASSWORD_HERE
```

Restart Redis:

```bash
sudo systemctl restart redis-server
```

## SSL/TLS with Cloudflare

### Install Certbot with Cloudflare plugin

```bash
sudo apt install -y certbot python3-certbot-nginx python3-pip
sudo pip3 install certbot-dns-cloudflare
```

### Create Cloudflare API token

1. Log in to your Cloudflare account
2. Go to "My Profile" > "API Tokens"
3. Create a new token with the following permissions:
   - Zone - DNS - Edit
   - Zone - Zone - Read
4. Restrict the token to the zone for minewache.de

### Create Cloudflare credentials file

```bash
sudo mkdir -p /etc/letsencrypt/cloudflare
sudo nano /etc/letsencrypt/cloudflare/credentials.ini
```

Add your Cloudflare API token:

```ini
dns_cloudflare_api_token = YOUR_CLOUDFLARE_API_TOKEN
```

Secure the credentials file:

```bash
sudo chmod 600 /etc/letsencrypt/cloudflare/credentials.ini
```

### Obtain wildcard certificate

```bash
sudo certbot certonly --dns-cloudflare --dns-cloudflare-credentials /etc/letsencrypt/cloudflare/credentials.ini -d minewache.de -d *.minewache.de
```

### Set up auto-renewal

Certbot automatically adds a cron job or systemd timer for certificate renewal. You can test the renewal process with:

```bash
sudo certbot renew --dry-run
```

## Service Management

The Minewache website requires several background services to function properly:

1. Queue Worker - Processes background jobs
2. Reverb WebSocket Server - Handles real-time communication
3. Discord Bot - Manages Discord integration

These services should be managed by systemd for proper startup, monitoring, and automatic restart in case of failure.

### Setting Up Services

The project includes a script that automatically sets up all required services as systemd units:

```bash
# Copy the script to the server if not already there
sudo cp /var/www/minewache-website/scripts/setup-production.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/setup-production.sh

# Run the script
sudo /usr/local/bin/setup-production.sh
```

This script will:

1. Create systemd service files for all required components
2. Set up proper logging
3. Configure services to start automatically on boot
4. Start all services immediately

### Checking Service Status

You can check the status of all services with:

```bash
sudo systemctl status minewache-queue.service
sudo systemctl status minewache-reverb.service
sudo systemctl status minewache-discord.service
```

### Managing Services

To restart a service:

```bash
sudo systemctl restart minewache-queue.service
```

To stop a service:

```bash
sudo systemctl stop minewache-reverb.service
```

To enable a service to start on boot:

```bash
sudo systemctl enable minewache-discord.service
```

To disable a service from starting on boot:

```bash
sudo systemctl disable minewache-queue.service
```

## Deployment Script

Create a deployment script for Ubuntu Server 24.0.1:

```bash
sudo nano /usr/local/bin/deploy-minewache.sh
```

Add the following content:

```bash
#!/bin/bash

# Minewache Website Deployment Script for Ubuntu Server 24.0.1
# This script deploys the Minewache website to production

# Exit on error
set -e

# Variables
PROJECT_DIR="/var/www/minewache-website"
BACKUP_DIR="/var/backups/minewache"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="/var/log/minewache-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log_message() {
  local level=$1
  local message=$2
  echo -e "${!level}[$(date '+%Y-%m-%d %H:%M:%S')] [${level}] ${message}${NC}" | tee -a $LOG_FILE
}

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup of the current installation
log_message "BLUE" "Creating backup of the current installation..."
if [ -d "$PROJECT_DIR" ]; then
  # Database backup
  if [ -f "$PROJECT_DIR/.env" ]; then
    source <(grep -v '^#' "$PROJECT_DIR/.env" | sed -E 's/^([^=]+)=(.*)$/export \1="\2"/g')
    mysqldump -u "$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" | gzip > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz"
    log_message "GREEN" "Database backup created: $BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz"
  else
    log_message "YELLOW" "No .env file found, skipping database backup."
  fi

  # Files backup
  tar --exclude="./vendor" --exclude="./node_modules" --exclude="./storage/logs" -czf "$BACKUP_DIR/files_backup_$TIMESTAMP.tar.gz" -C "$PROJECT_DIR" .
  log_message "GREEN" "Files backup created: $BACKUP_DIR/files_backup_$TIMESTAMP.tar.gz"
else
  log_message "YELLOW" "Project directory does not exist, skipping backup."
fi

# Put the application in maintenance mode if it exists
if [ -d "$PROJECT_DIR" ]; then
  log_message "BLUE" "Putting the application in maintenance mode..."
  cd "$PROJECT_DIR"
  php artisan down --message="Die Website wird aktualisiert und ist in Kürze wieder verfügbar." --retry=60
fi

# Pull the latest changes or clone the repository if it doesn't exist
if [ -d "$PROJECT_DIR/.git" ]; then
  log_message "BLUE" "Pulling the latest changes from git..."
  cd "$PROJECT_DIR"
  git pull
else
  log_message "BLUE" "Cloning the repository..."
  mkdir -p "$PROJECT_DIR"
  git clone https://github.com/yourusername/minewache-website.git "$PROJECT_DIR"
  cd "$PROJECT_DIR"
fi

# Install or update Composer dependencies
log_message "BLUE" "Installing Composer dependencies..."
cd "$PROJECT_DIR"
composer install --no-dev --optimize-autoloader

# Install or update npm dependencies
log_message "BLUE" "Installing npm dependencies..."
npm ci

# Build frontend assets
log_message "BLUE" "Building frontend assets..."
npm run build

# Set proper permissions
log_message "BLUE" "Setting proper permissions..."
chown -R www-data:www-data "$PROJECT_DIR"
find "$PROJECT_DIR" -type f -exec chmod 644 {} \;
find "$PROJECT_DIR" -type d -exec chmod 755 {} \;
chmod -R ug+rwx "$PROJECT_DIR/storage" "$PROJECT_DIR/bootstrap/cache"

# Run database migrations
log_message "BLUE" "Running database migrations..."
cd "$PROJECT_DIR"
php artisan migrate --force

# Clear and cache configuration
log_message "BLUE" "Clearing and caching configuration..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link if it doesn't exist
if [ ! -L "$PROJECT_DIR/public/storage" ]; then
  log_message "BLUE" "Creating storage link..."
  php artisan storage:link
fi

# Restart services
log_message "BLUE" "Restarting services..."
systemctl restart php8.3-fpm
systemctl restart minewache-queue.service
systemctl restart minewache-reverb.service
systemctl restart minewache-discord.service

# Take the application out of maintenance mode
log_message "BLUE" "Taking the application out of maintenance mode..."
php artisan up

log_message "GREEN" "Deployment completed successfully!"
```

Make the script executable:

```bash
sudo chmod +x /usr/local/bin/deploy-minewache.sh
```

You can run the deployment script with:

```bash
sudo /usr/local/bin/deploy-minewache.sh
```

## Monitoring

The Minewache website includes a monitoring script that creates a tmux session to monitor all services and logs in real-time.

### Prerequisites

Install tmux if not already installed:

```bash
sudo apt install -y tmux
```

### Using the Monitoring Script

```bash
# Copy the script to the server if not already there
sudo cp /var/www/minewache-website/scripts/monitor-production.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/monitor-production.sh

# Run the script
/usr/local/bin/monitor-production.sh
```

This script will create a tmux session with multiple windows:

1. **Services** - Shows the status of all systemd services
2. **Queue Logs** - Displays real-time logs from the queue worker
3. **Reverb Logs** - Displays real-time logs from the Reverb WebSocket server
4. **Discord Logs** - Displays real-time logs from the Discord bot
5. **Laravel Logs** - Displays real-time logs from the Laravel application
6. **Shell** - Provides a shell for running commands

### Navigating the Monitoring Session

- **Ctrl+B then 0-5**: Switch between windows
- **Ctrl+B then d**: Detach from session (keeps it running)
- **tmux attach -t minewache-monitor**: Reattach to session
- **tmux kill-session -t minewache-monitor**: Kill the session

## Cleanup

If you need to remove the Minewache services from your system, you can use the provided takedown script.

### Using the Takedown Script

```bash
# Copy the script to the server if not already there
sudo cp /var/www/minewache-website/scripts/takedown-production.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/takedown-production.sh

# Run the script
sudo /usr/local/bin/takedown-production.sh
```

This script will:

1. Stop and disable all Minewache systemd services
2. Remove the service files
3. Optionally remove the log files

The script includes confirmation prompts to prevent accidental removal.

### Manual Cleanup

If you prefer to manually remove the services:

```bash
# Stop and disable services
sudo systemctl stop minewache-queue.service
sudo systemctl disable minewache-queue.service
sudo systemctl stop minewache-reverb.service
sudo systemctl disable minewache-reverb.service
sudo systemctl stop minewache-discord.service
sudo systemctl disable minewache-discord.service

# Remove service files
sudo rm /etc/systemd/system/minewache-queue.service
sudo rm /etc/systemd/system/minewache-reverb.service
sudo rm /etc/systemd/system/minewache-discord.service

# Reload systemd
sudo systemctl daemon-reload
```
