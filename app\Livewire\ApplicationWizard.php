<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Application;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Validate;

class ApplicationWizard extends Component
{
    // Aktueller Schritt des Bewerbungsprozesses
    public $currentStep = 1;

    // Persönliche Daten (Schritt 1)
    #[Validate('required|string|max:255')]
    public $name;

    #[Validate('required|integer|min:14|max:120')]
    public $age;

    #[Validate('required|string|max:255')]
    public $gender;

    public $pronouns;

    #[Validate('required|array|min:1')]
    public $professions = [];

    #[Validate('nullable|required_if:professions.*,other|string|max:255')]
    public $otherProfession;

    // Rollenspezifische Daten (Schritt 2)
    public $portfolio;
    public $program;
    public $languages;
    public $ide;
    public $favorite_design;
    public $checkboxQuestions = [];
    public $has_pc;
    public $has_minecraft_java;
    public $ram;
    public $programming_languages;
    public $preferred_ide;
    public $java_experience;
    public $forge_experience;
    public $portfolio_projects;
    public $design_program;
    public $design_style;
    public $favorite_designs;
    public $fps;
    public $gpu;
    public $video_editing_program;
    public $voice_type;
    public $microphone;
    public $daw;
    public $desired_role;
    public $building_style;
    public $favorite_building_types;

    // Über dich (Schritt 3)
    #[Validate('required|string|min:50')]
    public $about_you;

    #[Validate('required|string|min:50')]
    public $strengths_weaknesses;

    public $final_words;

    // Bestätigung (Schritt 4 - Überprüfung)
    #[Validate('accepted')]
    public $confirmation = false;

    // Validierungsregeln wurden durch attributbasierte Validierung ersetzt

    // Verfügbare Berufsoptionen
    protected function getProfessionOptions() {
        return [
            'actor' => __('application.profession_description.actor'),
            'actor_no_voice' => __('application.profession_description.actor_no_voice'),
            'voice_actor' => __('application.profession_description.voice_actor'),
            'builder' => __('application.profession_description.builder'),
            'designer' => __('application.profession_description.designer'),
            'cutter' => __('application.profession_description.cutter'),
            'cameraman' => __('application.profession_description.cameraman'),
            'developer' => __('application.profession_description.developer'),
            'modeler' => __('application.profession_description.modeler'),
            'music_producer' => __('application.profession_description.music_producer'),
            'other' => __('application.profession_description.other')
        ];
    }

    protected $professionOptions = [];

    // Definiert, welche Felder für welche Berufe angezeigt werden
    protected $professionFields = [
        'developer' => [
            'has_pc', 'has_minecraft_java', 'ram', 'programming_languages',
            'preferred_ide', 'java_experience', 'forge_experience', 'portfolio_projects'
        ],
        'music_producer' => ['daw', 'portfolio_projects'],
        'cameraman' => ['has_pc', 'has_minecraft_java', 'ram', 'fps', 'gpu', 'portfolio_projects'],
        'designer' => [
            'has_pc', 'design_program', 'design_style', 'favorite_designs', 'portfolio_projects'
        ],
        'cutter' => ['has_pc', 'ram', 'gpu', 'video_editing_program', 'portfolio_projects'],
        'voice_actor' => ['voice_type', 'microphone', 'daw', 'portfolio_projects'],
        'actor' => ['has_pc', 'has_minecraft_java', 'ram', 'fps', 'desired_role'],
        'modeler' => ['has_pc', 'design_program', 'portfolio_projects'],
        'builder' => [
            'has_pc', 'has_minecraft_java', 'ram', 'building_style', 'favorite_building_types', 'portfolio_projects'
        ],
    ];

    // Livewire lifecycle hook - wird nach jeder Aktualisierung ausgeführt
    public function hydrate()
    {
        // Stellt sicher, dass professions immer ein Array ist, selbst wenn leere Daten übermittelt werden
        if (!is_array($this->professions)) {
            $this->professions = [];
        }
    }

    // Initialisierung der Komponente
    public function mount($applicationId = null)
    {
        // Initialize profession options with translations
        $this->professionOptions = $this->getProfessionOptions();

        // Wenn eine Bewerbungs-ID übergeben wurde, lade die Daten aus der Datenbank
        if ($applicationId) {
            $this->loadApplicationFromDatabase($applicationId);
        }
        // Ansonsten lade gespeicherte Daten aus der Session, falls vorhanden
        elseif (Session::has('application_data')) {
            $this->loadApplicationFromSession();
        }

        // Stellt sicher, dass professions immer ein Array ist
        if (!is_array($this->professions)) {
            $this->professions = [];
        }
    }

    /**
     * Lädt eine Bewerbung aus der Datenbank
     */
    private function loadApplicationFromDatabase($applicationId)
    {
        // Lade die Bewerbung
        $application = Application::where('id', $applicationId);

        // Wenn der Benutzer ein Admin ist, erlauben wir die Bearbeitung aller Bewerbungen
        if (!Gate::allows('MINEWACHE_TEAM')) {
            $application = $application->where('discord_id', Auth::id());
        }

        $application = $application->first();

        if (!$application) {
            session()->flash('error', __('application.application_not_found_or_no_access'));
            return;
        }

        // Prüfe, ob die Bewerbung bearbeitet werden darf (Admins dürfen immer bearbeiten)
        if (!$application->editable && !Gate::allows('MINEWACHE_TEAM')) {
            session()->flash('error', __('application.application_not_editable'));
            return;
        }

        // Speichere die Bewerbungs-ID für die spätere Verwendung
        $this->applicationId = $applicationId;

        // Persönliche Daten
        $this->name = $application->name;
        $this->age = $application->age;
        $this->gender = $application->gender;
        $this->pronouns = $application->pronouns;
        $this->professions = $application->professions ?? [];
        $this->otherProfession = $application->other_profession;

        // Rollenspezifische Daten
        $this->checkboxQuestions = $application->checkboxQuestions ?? [];
        $this->voice_type = $application->voice_type;
        $this->ram = $application->ram;
        $this->fps = $application->fps;
        $this->desired_role = $application->desired_role;
        $this->portfolio = $application->portfolio;
        $this->microphone = $application->microphone;
        $this->daw = $application->daw;
        $this->program = $application->program;
        $this->design_style = $application->design_style;
        $this->favorite_design = $application->favorite_design;
        $this->gpu = $application->gpu;
        $this->languages = $application->languages;
        $this->ide = $application->ide;

        // Über dich
        $this->about_you = $application->about_you;
        $this->strengths_weaknesses = $application->strengths_weaknesses;
        $this->final_words = $application->final_words;

        // Setze den aktuellen Schritt auf den Überprüfungsschritt
        $this->currentStep = 4;

        // Speichere die Daten in der Session
        $this->saveDataToSession();
    }

    /**
     * Lädt eine Bewerbung aus der Session
     */
    private function loadApplicationFromSession()
    {
        $data = Session::get('application_data');

        // Persönliche Daten
        $this->name = $data['name'] ?? null;
        $this->age = $data['age'] ?? null;
        $this->gender = $data['gender'] ?? null;
        $this->pronouns = $data['pronouns'] ?? null;
        $this->professions = $data['professions'] ?? [];
        $this->otherProfession = $data['otherProfession'] ?? null;

        // Rollenspezifische Daten
        $this->checkboxQuestions = $data['checkboxQuestions'] ?? [];
        $this->voice_type = $data['voice_type'] ?? null;
        $this->ram = $data['ram'] ?? null;
        $this->fps = $data['fps'] ?? null;
        $this->desired_role = $data['desired_role'] ?? null;
        $this->portfolio = $data['portfolio'] ?? null;
        $this->microphone = $data['microphone'] ?? null;
        $this->daw = $data['daw'] ?? null;
        $this->program = $data['program'] ?? null;
        $this->design_style = $data['design_style'] ?? null;
        $this->favorite_design = $data['favorite_design'] ?? null;
        $this->gpu = $data['gpu'] ?? null;
        $this->languages = $data['languages'] ?? null;
        $this->ide = $data['ide'] ?? null;
        // Über dich
        $this->about_you = $data['about_you'] ?? null;
        $this->strengths_weaknesses = $data['strengths_weaknesses'] ?? null;
        $this->final_words = $data['final_words'] ?? null;

        // Bestimme den aktuellen Schritt basierend auf den vorhandenen Daten
        if (isset($data['currentStep'])) {
            $this->currentStep = $data['currentStep'];
        } else {
            if (!empty($this->about_you) && !empty($this->strengths_weaknesses)) {
                $this->currentStep = 4; // Überprüfungsschritt
            } elseif (!empty($this->checkboxQuestions) || !empty($this->voice_type)) {
                $this->currentStep = 3; // Über dich
            } elseif (!empty($this->name) && !empty($this->age) && !empty($this->gender)) {
                $this->currentStep = 2; // Rollenspezifisch
            }
        }
    }

    // Wir verwenden jetzt attributbasierte Validierung statt getValidationRules() und rules() Methode

    // Custom validation messages using translations
    protected function messages()
    {
        return [
            'name.required' => __('validation.required', ['attribute' => __('application.name')]),
            'name.string' => __('validation.string', ['attribute' => __('application.name')]),
            'name.max' => __('validation.max.string', ['attribute' => __('application.name'), 'max' => 255]),

            'age.required' => __('validation.required', ['attribute' => __('application.age')]),
            'age.integer' => __('validation.integer', ['attribute' => __('application.age')]),
            'age.min' => __('validation.min.numeric', ['attribute' => __('application.age'), 'min' => 12]),
            'age.max' => __('validation.max.numeric', ['attribute' => __('application.age'), 'max' => 120]),

            'gender.required' => __('validation.required', ['attribute' => __('application.gender')]),
            'gender.string' => __('validation.string', ['attribute' => __('application.gender')]),
            'gender.max' => __('validation.max.string', ['attribute' => __('application.gender'), 'max' => 255]),

            'professions.required' => __('validation.required', ['attribute' => __('application.professions')]),
            'professions.array' => __('validation.array', ['attribute' => __('application.professions')]),
            'professions.min' => __('validation.min.array', ['attribute' => __('application.professions'), 'min' => 1]),

            'otherProfession.required_if' => __('validation.required_if', ['attribute' => __('application.other_profession'), 'other' => 'professions', 'value' => 'other']),
            'otherProfession.string' => __('validation.string', ['attribute' => __('application.other_profession')]),
            'otherProfession.max' => __('validation.max.string', ['attribute' => __('application.other_profession'), 'max' => 255]),

            'about_you.required' => __('validation.required', ['attribute' => __('application.about_you_text')]),
            'about_you.string' => __('validation.string', ['attribute' => __('application.about_you_text')]),
            'about_you.min' => __('validation.min.string', ['attribute' => __('application.about_you_text'), 'min' => 50]),

            'strengths_weaknesses.required' => __('validation.required', ['attribute' => __('application.strengths_weaknesses')]),
            'strengths_weaknesses.string' => __('validation.string', ['attribute' => __('application.strengths_weaknesses')]),
            'strengths_weaknesses.min' => __('validation.min.string', ['attribute' => __('application.strengths_weaknesses'), 'min' => 50]),

            'confirmation.accepted' => __('validation.accepted', ['attribute' => __('application.confirm_correct_information')]),
        ];
    }

    // Überwacht Änderungen an den Berufen
    public function updatedProfessions($value)
    {
        // Wenn ein Beruf entfernt wird, setze auch die zugehörigen Felder zurück
        $this->clearIrrelevantFields();

        // Speichert die Änderungen in der Session
        $this->saveDataToSession();
    }

    // Löscht Felder, die für die aktuell ausgewählten Berufe nicht relevant sind
    private function clearIrrelevantFields()
    {
        // Sammle alle relevanten Feldnamen für die ausgewählten Berufe
        $relevantFields = [];
        foreach ($this->professions as $profession) {
            if (isset($this->professionFields[$profession])) {
                $relevantFields = array_merge($relevantFields, $this->professionFields[$profession]);
            }
        }
        $relevantFields = array_unique($relevantFields);

        // Lösche Daten aus Feldern, die nicht mehr relevant sind
        $allPossibleFields = [
            'voice_type', 'microphone', 'ram', 'fps', 'gpu',
            'program', 'design_style', 'favorite_design', 'portfolio',
            'languages', 'ide', 'daw'
        ];

        foreach ($allPossibleFields as $field) {
            if (!in_array($field, $relevantFields)) {
                $this->{$field} = null;
            }
        }
    }

    // Speichert die aktuellen Daten in der Session
    protected function saveDataToSession()
    {
        // Stellt sicher, dass checkboxQuestions ein Array ist
        if (!is_array($this->checkboxQuestions)) {
            $this->checkboxQuestions = [];
        }

        $data = [
            // Aktueller Schritt
            'currentStep' => $this->currentStep,

            // Persönliche Daten
            'name' => $this->name,
            'age' => $this->age,
            'gender' => $this->gender,
            'pronouns' => $this->pronouns,
            'professions' => $this->professions,
            'otherProfession' => $this->otherProfession,

            // Rollenspezifische Daten
            'checkboxQuestions' => $this->checkboxQuestions, // Checkbox-Fragen speichern
            'voice_type' => $this->voice_type,
            'ram' => $this->ram,
            'fps' => $this->fps,
            'desired_role' => $this->desired_role,
            'portfolio' => $this->portfolio,
            'microphone' => $this->microphone,
            'daw' => $this->daw,
            'program' => $this->program,
            'design_style' => $this->design_style,
            'favorite_design' => $this->favorite_design,
            'gpu' => $this->gpu,
            'languages' => $this->languages,
            'ide' => $this->ide,

            // Über dich
            'about_you' => $this->about_you,
            'strengths_weaknesses' => $this->strengths_weaknesses,
            'final_words' => $this->final_words,
        ];

        Session::put('application_data', $data);
    }

    // Zum nächsten Schritt gehen
    public function nextStep()
    {
        try {
            // Validiere die Daten für den aktuellen Schritt mit schritt-spezifischen Regeln
            if ($this->currentStep === 1) {
                $this->validate([
                    'name' => 'required|string|max:255',
                    'age' => 'required|integer|min:14|max:120',
                    'gender' => 'required|string|max:255',
                    'professions' => 'required|array|min:1',
                    'otherProfession' => 'nullable|required_if:professions.*,other|string|max:255',
                ]);

                // Stelle sicher, dass irrelevante Felder zurückgesetzt sind
                $this->clearIrrelevantFields();
            } elseif ($this->currentStep === 3) {
                $this->validate([
                    'about_you' => 'required|string|min:50',
                    'strengths_weaknesses' => 'required|string|min:50',
                ]);
            } elseif ($this->currentStep === 4) {
                $this->validate([
                    'confirmation' => 'accepted',
                ]);
            }

            // Gehe zum nächsten Schritt
            $this->currentStep++;

            // Speichere die Daten in der Session
            $this->saveDataToSession();
        } catch (ValidationException $e) {
            // Bei Validierungsfehlern für professions speziell behandeln
            if (isset($e->validator->failed()['professions'])) {
                session()->flash('professions_error', __('application.please_select_at_least_one_profession'));
            }
            throw $e;
        }
    }

    // Toggle profession selection
    public function toggleProfession($profession)
    {
        if (!is_array($this->professions)) {
            $this->professions = [];
        }

        if (in_array($profession, $this->professions)) {
            // Remove profession
            $this->professions = array_filter($this->professions, function($p) use ($profession) {
                return $p !== $profession;
            });
        } else {
            // Add profession
            $this->professions[] = $profession;
        }

        // Re-index array to avoid gaps
        $this->professions = array_values($this->professions);

        // Save data to session
        $this->saveDataToSession();
    }

    // Zum vorherigen Schritt zurückgehen
    public function previousStep()
    {
        // Gehe zum vorherigen Schritt, aber nicht unter 1
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }

        // Speichere die Daten in der Session
        $this->saveDataToSession();
    }

    // Aktualisiert ein Feld und speichert es in der Session
    public function updated($field, $value)
    {
        // Speichere die Änderungen in der Session
        $this->saveDataToSession();
    }

    // Bewerbungs-ID für die Bearbeitung
    public $applicationId = null;

    // Bewerbung absenden
    public function submitApplication()
    {
        // Validiere die Bestätigung für den aktuellen Schritt
        $this->validate([
            'confirmation' => 'accepted',
        ]);

        try {
            // Stelle sicher, dass alle vorherigen Schritte auch validiert werden
            $allValid = true;

            // Temporär den currentStep auf jeden Schritt setzen und validieren
            $originalStep = $this->currentStep;

            // Wir validieren jeden Schritt manuell mit den entsprechenden Regeln
            $step1Rules = [
                'name' => 'required|string|max:255',
                'age' => 'required|integer|min:14|max:120',
                'gender' => 'required|string|max:255',
                'professions' => 'required|array|min:1',
                'otherProfession' => 'nullable|required_if:professions.*,other|string|max:255',
            ];

            $step3Rules = [
                'about_you' => 'required|string|min:50',
                'strengths_weaknesses' => 'required|string|min:50',
            ];

            try {
                $this->validate($step1Rules);
                $this->validate($step3Rules);
            } catch (ValidationException $e) {
                $allValid = false;
                // Bestimme den Schritt basierend auf den fehlgeschlagenen Validierungen
                if (array_intersect_key($e->validator->failed(), array_flip(['name', 'age', 'gender', 'professions', 'otherProfession']))) {
                    $this->currentStep = 1;
                } else {
                    $this->currentStep = 3;
                }
                throw $e;
            }

            // Zurück zum Überprüfungsschritt
            $this->currentStep = $originalStep;

            if (!$allValid) {
                return;
            }

            // Prüfe, ob wir eine bestehende Bewerbung bearbeiten oder eine neue erstellen
            if ($this->applicationId) {
                // Lade die bestehende Bewerbung
                $application = Application::where('id', $this->applicationId);

                // Wenn der Benutzer ein Admin ist, erlauben wir die Bearbeitung aller Bewerbungen
                if (!Gate::allows('MINEWACHE_TEAM')) {
                    $application = $application->where('discord_id', Auth::id());
                }

                $application = $application->first();

                if (!$application) {
                    session()->flash('error', __('application.application_not_found_or_no_access'));
                    return;
                }

                // Prüfe, ob die Bewerbung bearbeitet werden darf (Admins dürfen immer bearbeiten)
                if (!$application->editable && !Gate::allows('MINEWACHE_TEAM')) {
                    session()->flash('error', __('application.application_not_editable'));
                    return;
                }

                // Aktualisiere die Bewerbung
                $logMessage = 'Bewerbung erfolgreich aktualisiert';
                $successMessage = __('application.application_updated') . '! ' . __('application.we_will_contact');
            } else {
                // Erstelle eine neue Bewerbung
                $application = new Application();
                $application->discord_id = Auth::id();
                $application->created_at = now();
                $logMessage = 'Bewerbung erfolgreich eingereicht';
                $successMessage = __('application.application_submitted') . '! ' . __('application.we_will_contact');
            }

            // Gemeinsame Felder für neue und bestehende Bewerbungen
            $application->name = $this->name;
            $application->age = $this->age;
            $application->gender = $this->gender;
            $application->pronouns = $this->pronouns;
            $application->professions = $this->professions;

            // Wenn "other" ausgewählt wurde, fügen wir die benutzerdefinierte Tätigkeit hinzu
            if(in_array('other', $this->professions) && !empty($this->otherProfession)) {
                $application->other_profession = $this->otherProfession;
            }

            $application->checkboxQuestions = $this->checkboxQuestions;
            $application->about_you = $this->about_you;
            $application->strengths_weaknesses = $this->strengths_weaknesses;
            $application->final_words = $this->final_words;
            $application->voice_type = $this->voice_type;
            $application->ram = $this->ram;
            $application->fps = $this->fps;
            $application->desired_role = $this->desired_role;
            $application->portfolio = $this->portfolio;
            $application->microphone = $this->microphone;
            $application->daw = $this->daw;
            $application->program = $this->program;
            $application->design_style = $this->design_style;
            $application->favorite_design = $this->favorite_design;
            $application->gpu = $this->gpu;
            $application->languages = $this->languages;
            $application->ide = $this->ide;
            $application->status = 'pending'; // Standardstatus
            $application->editable = false; // Standardmäßig bearbeitbar
            $application->updated_at = now();
            $application->save();

            // Protokolliere erfolgreiche Bewerbung
            Log::info($logMessage, [
                'user_id' => Auth::id(),
                'application_id' => $application->id
            ]);

            // Lösche die Session-Daten nach erfolgreicher Bewerbung
            Session::forget('application_data');

            // Erfolgreiche Rückmeldung mit Animation-Hint
            session()->flash('success', $successMessage);
            session()->flash('animation', 'slide-up');
            session()->flash('application_id', $application->id);

            // Weiterleitung zur Erfolgsseite
            return redirect()->route('applications.success');

        } catch (\Exception $e) {
            // Protokolliere Fehler
            Log::error('Fehler beim Speichern der Bewerbung', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fehlermeldung anzeigen
            session()->flash('error', __('messages.error') . ': ' . __('messages.please_try_again_later'));

            // Bleibe auf der aktuellen Seite
            return null;
        }
    }

    // Render-Methode für die View
    public function render()
    {
        // Refresh profession options to ensure translations are up-to-date
        $this->professionOptions = $this->getProfessionOptions();

        return view('livewire.application-wizard', [
            'professionOptions' => $this->professionOptions,
        ]);
    }
}
