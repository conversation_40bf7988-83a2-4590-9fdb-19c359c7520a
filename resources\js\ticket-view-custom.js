// resources/js/ticket-view-custom.js

// Alpine component for ticket view
document.addEventListener('alpine:init', () => {
    Alpine.data('ticketView', () => ({
        ticketId: null,
        messagesContainer: null,
        newMessageIndicator: null,
        scrollToBottomBtn: null,
        soundBase64: 'data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAADwAD///////////////////////////////////////////8AAAA8TEFNRTMuMTAwAc0AAAAAAAAAABSAJAJAQgAAgAAAA8DcWcGJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQZAAP8AAAaQAAAAgAAA0gAAABAAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVU=',
        notificationSound: null,
        soundLoaded: false,

        init() {
            // Use Livewire 3's component.initialized hook instead of polling
            if (window.Livewire && window.Livewire.hook) {
                Livewire.hook('component.initialized', (component) => {
                    if (component.name === 'ticket-view') {
                        this.initializeAfterLivewire();
                    }
                });
            } else {
                // Fallback: Wait for Livewire to be ready and DOM to be available
                this.$nextTick(() => {
                    this.initializeAfterLivewire();
                });
            }
        },

        initializeAfterLivewire() {
            const maxRetries = 10;
            let retryCount = 0;

            const attemptInitialization = () => {
                // Guard: ensure $wire is available
                if (!this.$wire || !this.$wire.ticket) {
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        console.error('Failed to initialize ticket view: Livewire $wire not available after maximum retries');
                        return;
                    }
                    console.warn(`Livewire $wire not ready, retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(attemptInitialization, 100);
                    return;
                }

                // Initialize references with safety checks
                this.ticketId = this.$wire.ticket.id;
                this.messagesContainer = document.getElementById('messages-container');
                this.newMessageIndicator = document.getElementById('new-message-indicator');
                this.scrollToBottomBtn = document.getElementById('scroll-to-bottom');

                // Guard: if critical elements are missing, retry after a short delay
                if (!this.messagesContainer) {
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        console.error('Failed to initialize ticket view: Messages container not found after maximum retries');
                        return;
                    }
                    console.warn(`Messages container not found, retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(attemptInitialization, 100);
                    return;
                }

                // Reset retry count on successful initialization
                retryCount = 0;
                this.setupEventListeners();
                this.initializeSound(); // Initialize sound
            };

            attemptInitialization();
        },

        initializeSound() {
            this.notificationSound = new Audio(this.soundBase64);
            this.notificationSound.preload = 'auto';

            this.notificationSound.addEventListener('canplaythrough', () => {
                console.log('Notification sound loaded and ready to play');
                this.soundLoaded = true;
            });

            this.notificationSound.addEventListener('error', (e) => {
                console.error('Error loading notification sound:', e);
                this.soundLoaded = false;
            });
        },

        isSoundEnabled() {
            const soundToggle = document.getElementById('sound-toggle');
            return soundToggle ? soundToggle.checked : true; // Default to true if toggle not found
        },

        playFallbackSound() {
            try {
                // Create a new Audio instance as fallback using the base64 sound
                const fallbackAudio = new Audio(this.soundBase64);
                fallbackAudio.volume = 0.5;
                fallbackAudio.play().catch(error => {
                    console.error('Fallback sound also failed:', error);
                    // Last resort: try a simple beep sound
                    try {
                        // Simple beep using AudioContext API
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        oscillator.type = 'sine';
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // value in hertz
                        oscillator.connect(audioContext.destination);
                        oscillator.start();
                        oscillator.stop(audioContext.currentTime + 0.2); // short beep
                    } catch (e) {
                        console.error('All sound playback attempts failed:', e);
                    }
                });
            } catch (error) {
                console.error('Error creating fallback Audio object:', error);
            }
        },

        setupEventListeners() {

            // Scroll-to-bottom button click
            if (this.scrollToBottomBtn) {
                this.scrollToBottomBtn.addEventListener('click', () => {
                    this.scrollToEnd();
                });
            }

            // Listen to scroll events to toggle indicators
            if (this.messagesContainer) {
                this.messagesContainer.addEventListener('scroll', () => {
                    const buffer = 200;
                    const atBottom = this.messagesContainer.scrollHeight - this.messagesContainer.scrollTop <= this.messagesContainer.clientHeight + buffer;
                    if (atBottom) {
                        this.newMessageIndicator?.classList.add('hidden');
                        this.scrollToBottomBtn?.classList.add('hidden');
                    } else {
                        this.scrollToBottomBtn?.classList.remove('hidden');
                    }
                });
            }

            // Laravel Echo real-time subscriptions
            if (window.Echo && this.ticketId) {
                window.Echo.private(`tickets.${this.ticketId}`)
                    .listen('MessageAdded', e => {
                        this.handleMessageAdded(e.message);
                    })
                    .listenForWhisper('typing', e => {
                        this.handleTypingUpdate(e.users);
                    });
            }

            // Listen for Livewire events
            if (window.Livewire) {
                Livewire.on('ticket-message-sent', (data) => {
                    this.scrollToEnd(); // Scroll to end when current user sends a message
                });

                // Optional: If 'ticket-updated' implies a scroll is needed beyond new messages
                // Livewire.on('ticket-updated', () => {
                //     this.scrollToEnd();
                // });
            }
        },

        // Scroll container to bottom
        scrollToEnd() {
            if (!this.messagesContainer) return;
            this.messagesContainer.scrollTo({
                top: this.messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
            this.newMessageIndicator?.classList.add('hidden');
            this.scrollToBottomBtn?.classList.add('hidden');
        },

        // Called when a new message is added via Livewire or Echo
        handleMessageAdded(message) {
            this.playNotificationSound(); // Play sound on new message

            // Let Livewire update the DOM, then scroll
            this.$nextTick(() => { // Use $nextTick for Alpine DOM updates
                const isScrolledToBottom = this.messagesContainer.scrollHeight - this.messagesContainer.clientHeight <= this.messagesContainer.scrollTop + 100;

                if (!isScrolledToBottom && this.newMessageIndicator) {
                    this.newMessageIndicator.classList.remove('hidden');
                    // Attach event listener to indicator to scroll down and hide
                    this.newMessageIndicator.addEventListener('click', () => {
                        this.scrollToEnd();
                        this.newMessageIndicator.classList.add('hidden');
                    }, { once: true });
                } else {
                    this.scrollToEnd();
                }
            });
        },

        // Play notification sound
        playNotificationSound() {
            // Only play if sound is enabled
            if (!this.isSoundEnabled()) {
                console.log('Sound notifications are disabled');
                return;
            }

            try {
                console.log('Playing notification sound');

                // If the preloaded sound is ready, use it
                if (this.soundLoaded) {
                    // Reset the sound to the beginning if it's already playing
                    this.notificationSound.currentTime = 0;

                    // Play the sound
                    const playPromise = this.notificationSound.play();

                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.error('Error playing notification sound:', error);
                            // Try alternative approach if the main one fails
                            this.playFallbackSound();
                        });
                    }
                } else {
                    // If preloaded sound isn't ready, try a new instance
                    this.playFallbackSound();
                }
            } catch (error) {
                console.error('Error playing notification sound:', error);
                this.playFallbackSound();
            }
        },

        // Attach attachments to a message element
        handleAttachmentsReady(messageId, attachments) {
            if (!this.messagesContainer) return;
            const msgEl = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
            if (!msgEl || !Array.isArray(attachments)) return;

            let container = msgEl.querySelector('.attachments-container');
            if (!container) {
                container = document.createElement('div');
                container.classList.add('attachments-container', 'mt-3', 'pt-3', 'border-t');
                msgEl.querySelector('.prose')?.after(container);
            }
            attachments.forEach(file => {
                const a = document.createElement('a');
                a.href = file.url;
                a.textContent = file.name;
                a.target = '_blank';
                a.rel = 'noopener';
                a.className = 'block text-blue-400 underline mb-1';
                container.appendChild(a);
            });
        },

        // Show/hide typing indicator
        handleTypingUpdate(users) {
            const typingEl = document.getElementById('typing-indicator');
            if (!typingEl) return;

            const userNames = Array.isArray(users) ? users : Object.values(users || {});
            if (userNames.length === 0) {
                typingEl.classList.add('hidden');
            } else {
                const nameSpan = typingEl.querySelector('.typing-user-name'); // Assuming this is the target for the text
                if (nameSpan) {
                    let text = '';
                    if (userNames.length === 1) {
                        text = `${userNames[0]} schreibt...`;
                    } else if (userNames.length === 2) {
                        text = `${userNames[0]} und ${userNames[1]} schreiben...`;
                    } else {
                        text = `${userNames.length} Personen schreiben...`;
                    }
                    nameSpan.textContent = text;
                }
                typingEl.classList.remove('hidden');
            }
        },

        // Debounce function
        debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }
    }));
});

// Utility: copy text to clipboard
window.copyToClipboard = function(text) {
    if (!text) return;
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).catch(() => {});
    } else {
        const ta = document.createElement('textarea');
        ta.value = text;
        ta.style.position = 'fixed';
        ta.style.opacity = '0';
        document.body.appendChild(ta);
        ta.select();
        document.execCommand('copy');
        document.body.removeChild(ta);
    }
};

// Utility: cancel current upload
window.handleCancelUpload = function() {
    // Clear Livewire attachments
    if (window.Livewire && Livewire.emit) {
        Livewire.emit('clearAttachments');
    }
    // Reset the file input
    const inp = document.getElementById('attachments');
    if (inp) inp.value = '';
};

// SPA-style navigation for internal links within the ticket view
document.addEventListener('click', function(e) {
    const link = e.target.closest('#ticket-view a[href]');
    if (!link) return;
    const href = link.getAttribute('href');
    if (!href.startsWith('/') || link.hasAttribute('target') || link.dataset.noSpa !== undefined) {
        return; // external or opted-out
    }
    e.preventDefault();
    history.pushState({}, '', href);
    // Let Livewire or full reload handle the route
    if (window.Livewire && Livewire.visit) {
        Livewire.visit(href);
    } else {
        window.location.href = href;
    }
});

// Handle browser navigation (back/forward)
window.addEventListener('popstate', () => {
    if (window.Livewire && Livewire.visit) {
        Livewire.visit(location.pathname);
    }
});
