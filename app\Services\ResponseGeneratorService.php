<?php

namespace App\Services;

use App\Models\Application;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ResponseGeneratorService
{
    private $roleTranslations = [
        'actor' => '<PERSON><PERSON><PERSON><PERSON>',
        'actor_no_voice' => '<PERSON><PERSON><PERSON><PERSON> (no voice)',
        'voice_actor' => 'Synchronsprecher',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'cutter' => 'Cutter',
        'cameraman' => 'Kameramann',
        'developer' => 'Developer',
        'modeler' => 'Modellierer',
        'music_producer' => 'Musikproduzent',
    ];

    private $roleInstructions = [
        'Schauspieler' => 'Wie Sie vielleicht schon wissen, nehmen wir immer samstags auf. Daher achten Sie bitte immer auf den ⁠Events Kanal und reagieren Sie dort mit 🔴 wenn Sie nicht können, 🟡 wenn Sie vielleicht können, und mit 🟢 wenn Sie auf jeden Fall können. Sollten Sie grün angegeben haben und nicht zur Aufnahme er<PERSON>, erhalten Sie einen Warnhinweis.',
        'Schauspieler-no-voice' => 'Wie Sie vielleicht schon wissen, nehmen wir immer samstags auf. Daher achten Sie bitte immer auf den ⁠Events Kanal und reagieren Sie dort mit 🔴 wenn Sie nicht können, 🟡 wenn Sie vielleicht können, und mit 🟢 wenn Sie auf jeden Fall können. Sollten Sie grün angegeben haben und nicht zur Aufnahme erscheinen, erhalten Sie einen Warnhinweis.',
        'Synchronsprecher' => 'Bitte schicken sie uns für die Rolle Synchronsprecher, eine Audioaufnahme an <@658671734161014785> .',
        'Developer' => 'Wir bitten Sie, sich für die Tätigkeit als Developer im [Developer-Kanal](https://discord.com/channels/1031202173826109591/1163966476731031572) zu melden sowie ihre Programmiersprachen zu nennen.',
        'Kameramann' => 'Bitte lesen Sie sich [hier](https://discord.com/channels/1031202173826109591/1043278886127013888/1180292894909800562) alle Informationen zum Kameramann durch.',
        'Designer' => 'Wir bitten Sie, sich für die Tätigkeit als Designer im [Designer-Kanal](https://discord.com/channels/1031202173826109591/1056925227172511784) zu melden.',
        'Cutter' => 'Wir bitten Sie, sich für die Tätigkeit als Cutter im [Cutter-Kanal](https://discord.com/channels/1031202173826109591/1171201784119824404) zu melden.',
        'Modellierer' => 'Wir bitten Sie, sich für die Tätigkeit als Modellierer im [Modellierer-Kanal](https://discord.com/channels/1031202173826109591/1056925227172511784) zu melden.',
        'Builder' => 'Wir bitten Sie, sich für die Tätigkeit als Builder den [Bauleitfaden](https://discord.com/channels/1031202173826109591/1259601721269944380) und [Infos-Builder](https://discord.com/channels/1031202173826109591/1056924495413260358) anzuschauen und sich hierzu zu engagieren.',
        'Musikproduzent' => 'Wir bitten Sie, sich für die Tätigkeit als Musikproduzent im [Musik-Kanal](https://discord.com/channels/1031202173826109591/1074734540066672711) zu melden.',
    ];

    private $rejectionReasons = [
        "RP" => "Info: Wie es uns scheint denken Sie wohlmöglich, dass wir ein RP-Server sind. Dies ist aber NICHT der Fall. Wir drehen nur eine Minecraft-Serie.",
        "PCMC" => "Nach Ihren Angaben besitzen Sie keinen PC und kein Minecraft Java, was Sie jedoch zur Nutzung unseres Modpacks benötigen.",
        "PC" => "Nach Ihren Angaben besitzen Sie keinen PC, was Sie jedoch zur Nutzung unseres Modpacks benötigen.",
        "INSUFFICIENT_RAM" => "Nach Ihren Angaben verfügen Sie über zu wenig RAM, um unsere Modifikationen flüssig ausführen zu können. Für die gewünschte Rolle werden mindestens 8 GB RAM empfohlen.",
        "INSUFFICIENT_FPS" => "Nach Ihren Angaben erreichen Sie zu wenig FPS, um unsere Modifikationen flüssig ausführen zu können. Für die gewünschte Rolle werden mindestens 30 FPS empfohlen.",
        "NO_MICROPHONE" => "Für die gewünschte Rolle benötigen Sie ein Mikrofon, was laut Ihren Angaben nicht vorhanden ist.",
        "NO_DAW" => "Für die Rolle als Musikproduzent benötigen Sie eine Digital Audio Workstation (DAW), was laut Ihren Angaben nicht vorhanden ist.",
        "TOO_YOUNG" => "Leider müssen wir Ihre Bewerbung ablehnen, da Sie mit einem Alter von unter 14 Jahren zu jung für die Teilnahme an unserem Projekt sind. Aus Gründen des Jugendschutzes und der Teamdynamik können wir nur Bewerber ab 14 Jahren berücksichtigen."
    ];

    // Minimale Anforderungen für bestimmte Rollen
    private $roleRequirements = [
        'actor' => ['ram' => 8, 'fps' => 30],
        'actor_no_voice' => ['ram' => 8, 'fps' => 30],
        'cameraman' => ['ram' => 12, 'fps' => 60],
        'builder' => ['ram' => 8, 'fps' => 30],
        'designer' => [],
        'cutter' => [],
        'voice_actor' => ['microphone' => true],
        'developer' => [],
        'modeler' => [],
        'music_producer' => ['daw' => true],
    ];

    /**
     * Analysiert die Bewerbung und schlägt automatisch Ablehnungsgründe vor
     * Implementiert Caching für bessere Performance
     *
     * @param Application $application Die zu analysierende Bewerbung
     * @return array Array mit vorgeschlagenen Ablehnungsgründen
     */
    public function suggestRejectionReasons(Application $application): array
    {
        if (!$application || !$application->id) {
            Log::warning('Ungültige Bewerbung für suggestRejectionReasons übergeben');
            return [];
        }

        try {
            // Cache-Key basierend auf der Bewerbungs-ID und dem letzten Update-Zeitpunkt
            $cacheKey = 'application_rejection_reasons_' . $application->id . '_' . $application->updated_at->timestamp;

            Log::debug('Ablehnungsgründe berechnen oder aus Cache laden', [
                'application_id' => $application->id,
                'cache_key' => $cacheKey
            ]);

            // Versuche, das Ergebnis aus dem Cache zu holen (5 Minuten gültig)
            return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($application) {
                Log::info('Cache-Miss: Berechne Ablehnungsgründe für Bewerbung', ['application_id' => $application->id]);
                $suggestedReasons = [];

        // Prüfen auf RP-Server Missverständnis
        if ($application->about_you &&
            (stripos($application->about_you, 'roleplay') !== false ||
             stripos($application->about_you, 'rp') !== false ||
             stripos($application->about_you, 'rollenspiel') !== false)) {
            $suggestedReasons[] = 'RP';
        }

        // Prüfen auf PC-Anforderungen
        if ($application->ram && $application->ram < 4) {
            // Wenn RAM zu niedrig ist, könnte das auf einen schwachen PC oder keinen PC hindeuten
            $suggestedReasons[] = 'PC';
        }

        // Prüfen auf Minecraft Java
        if ($application->checkboxQuestions &&
            is_array($application->checkboxQuestions) &&
            in_array('no_minecraft_java', $application->checkboxQuestions)) {
            $suggestedReasons[] = 'PCMC';
        }

        // Prüfen auf Alter
        if ($application->age && $application->age < 14) {
            $suggestedReasons[] = 'TOO_YOUNG';
        }

        // Prüfen auf Rollenanforderungen
        if (is_array($application->professions)) {
            foreach ($application->professions as $role) {
                if (isset($this->roleRequirements[$role])) {
                    $requirements = $this->roleRequirements[$role];

                    // RAM-Anforderung prüfen
                    if (isset($requirements['ram']) &&
                        $application->ram &&
                        $application->ram < $requirements['ram']) {
                        $suggestedReasons[] = 'INSUFFICIENT_RAM';
                    }

                    // FPS-Anforderung prüfen
                    if (isset($requirements['fps']) &&
                        $application->fps &&
                        intval($application->fps) < $requirements['fps']) {
                        $suggestedReasons[] = 'INSUFFICIENT_FPS';
                    }

                    // Mikrofon-Anforderung prüfen
                    if (isset($requirements['microphone']) &&
                        $requirements['microphone'] &&
                        empty($application->microphone)) {
                        $suggestedReasons[] = 'NO_MICROPHONE';
                    }

                    // DAW-Anforderung prüfen
                    if (isset($requirements['daw']) &&
                        $requirements['daw'] &&
                        empty($application->daw)) {
                        $suggestedReasons[] = 'NO_DAW';
                    }
                }
            }
        }

                $result = array_unique($suggestedReasons);
                Log::debug('Berechnete Ablehnungsgründe', [
                    'application_id' => $application->id,
                    'reasons_count' => count($result),
                    'reasons' => $result
                ]);
                return $result;
            });
        } catch (\Exception $e) {
            Log::error('Fehler beim Berechnen der Ablehnungsgründe', [
                'application_id' => $application->id,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Generiere eine vollständige Antwort für eine Bewerbung
     *
     * @param Application $application Die zu bearbeitende Bewerbung
     * @param string $status Der Status der Bewerbung (approved, rejected, pending)
     * @param array $selectedReasons Array mit ausgewählten Ablehnungsgründen
     * @param string $customReasons Benutzerdefinierte Ablehnungsgründe
     * @param bool $useSmartSuggestions Ob automatische Vorschläge verwendet werden sollen
     * @return array Die generierte Antwort mit separaten Feldern für Markdown und kopierbaren Text
     */
    public function generateFullResponse(Application $application, string $status, array $selectedReasons = [], string $customReasons = '', bool $useSmartSuggestions = true): array
    {
        // Logging für besseres Debugging
        Log::info('GenerateFullResponse aufgerufen', [
            'application_id' => $application->id,
            'status' => $status,
            'reasons_count' => count($selectedReasons),
            'custom_reasons_length' => strlen($customReasons),
            'use_smart' => $useSmartSuggestions
        ]);

        try {
            // Validiere Eingabeparameter
            if (!in_array($status, ['approved', 'rejected', 'pending'])) {
                Log::warning('Ungültiger Status übergeben', ['status' => $status]);
                $status = 'pending'; // Fallback auf "pending" als sicheren Standardwert
            }

            // Wenn Smart-Suggestions aktiviert sind und keine manuellen Gründe ausgewählt wurden
            if ($useSmartSuggestions && $status === 'rejected' && empty($selectedReasons)) {
                // Automatische Ablehnungsgründe vorschlagen
                $selectedReasons = $this->suggestRejectionReasons($application);
                Log::info('Automatische Ablehnungsgründe vorgeschlagen', ['reasons' => $selectedReasons]);
            }

            $markdownResponse = $this->generateMarkdownResponse($application, $status, $selectedReasons, $customReasons);
            $plainTextResponse = $this->generatePlainTextResponse($application, $status, $selectedReasons, $customReasons);

            $suggestedReasons = [];
            if ($useSmartSuggestions) {
                $suggestedReasons = $this->suggestRejectionReasons($application);
            }

            return [
                'markdown' => $markdownResponse,
                'copyable' => $plainTextResponse,
                'suggested_reasons' => $suggestedReasons
            ];
        } catch (\Exception $e) {
            Log::error('Fehler beim Generieren der Antwort', [
                'application_id' => $application->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback-Antwort im Fehlerfall
            return [
                'markdown' => 'Es ist ein Fehler beim Generieren der Antwort aufgetreten.',
                'copyable' => 'Es ist ein Fehler beim Generieren der Antwort aufgetreten.',
                'suggested_reasons' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generiert eine Standardantwort basierend auf den Bewerbungsdaten
     *
     * @param Application $application Die zu bearbeitende Bewerbung
     * @return array Die generierte Antwort mit separaten Feldern für Markdown und kopierbaren Text
     */
    public function generateSmartResponse(Application $application): array
    {
        // Automatisch den Status bestimmen
        $analysis = $this->analyzeApplication($application);
        $status = $analysis['recommended_status'];
        $suggestedReasons = $analysis['rejection_reasons'];

        return $this->generateFullResponse($application, $status, $suggestedReasons, '', true);
    }

    /**
     * Analysiert eine Bewerbung und gibt Empfehlungen
     * Implementiert Caching für bessere Performance
     *
     * @param Application $application Die zu analysierende Bewerbung
     * @return array Analyseergebnisse mit Empfehlungen
     */
    public function analyzeApplication(Application $application): array
    {
        if (!$application || !$application->id) {
            Log::warning('Ungültige Bewerbung für analyzeApplication übergeben');
            return [
                'recommended_status' => 'pending',
                'rejection_reasons' => [],
                'quality_score' => 0,
                'strengths' => [],
                'weaknesses' => [],
            ];
        }

        try {
            // Cache-Key basierend auf der Bewerbungs-ID und dem letzten Update-Zeitpunkt
            $cacheKey = 'application_analysis_' . $application->id . '_' . $application->updated_at->timestamp;

            Log::debug('Bewerbungsanalyse aus Cache laden oder neu berechnen', [
                'application_id' => $application->id,
                'cache_key' => $cacheKey
            ]);

            // Versuche, das Ergebnis aus dem Cache zu holen (5 Minuten gültig)
            return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($application) {
                Log::info('Cache-Miss: Analysiere Bewerbung', ['application_id' => $application->id]);
                $suggestedReasons = $this->suggestRejectionReasons($application);
                $recommendedStatus = empty($suggestedReasons) ? 'approved' : 'rejected';

                // Bewertung der Bewerbungsqualität
                $quality = $this->assessApplicationQuality($application);

                $strengths = $this->identifyStrengths($application);
                $weaknesses = $this->identifyWeaknesses($application);

                $result = [
                    'recommended_status' => $recommendedStatus,
                    'rejection_reasons' => $suggestedReasons,
                    'quality_score' => $quality,
                    'strengths' => $strengths,
                    'weaknesses' => $weaknesses,
                ];

                Log::debug('Bewerbungsanalyse abgeschlossen', [
                    'application_id' => $application->id,
                    'status' => $recommendedStatus,
                    'quality' => $quality,
                    'strengths_count' => count($strengths),
                    'weaknesses_count' => count($weaknesses)
                ]);

                return $result;
            });
        } catch (\Exception $e) {
            Log::error('Fehler bei der Bewerbungsanalyse', [
                'application_id' => $application->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback für Fehlerfall
            return [
                'recommended_status' => 'pending', // Im Fehlerfall auf "pending" setzen
                'rejection_reasons' => [],
                'quality_score' => 50, // Neutraler Wert
                'strengths' => [],
                'weaknesses' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bewertet die Qualität einer Bewerbung
     *
     * @param Application $application Die zu bewertende Bewerbung
     * @return int Qualitätsbewertung (0-100)
     */
    private function assessApplicationQuality(Application $application): int
    {
        $score = 50; // Ausgangswert

        // Länge der Texte bewerten
        if (!empty($application->about_you)) {
            $length = strlen($application->about_you);
            if ($length > 500) $score += 10;
            else if ($length > 200) $score += 5;
        }

        if (!empty($application->strengths_weaknesses)) {
            $length = strlen($application->strengths_weaknesses);
            if ($length > 300) $score += 10;
            else if ($length > 100) $score += 5;
        }

        // Vollständigkeit der Bewerbung
        $requiredFields = ['name', 'age', 'gender', 'professions', 'about_you', 'strengths_weaknesses'];
        $filledFields = 0;

        foreach ($requiredFields as $field) {
            if (!empty($application->$field)) {
                $filledFields++;
            }
        }

        $completeness = ($filledFields / count($requiredFields)) * 100;
        $score += ($completeness >= 80) ? 15 : (($completeness >= 60) ? 10 : 0);

        // Alter bewerten
        if ($application->age) {
            // Mindestalter 14
            if ($application->age < 14) {
                $score -= 30; // Starker Abzug für zu junges Alter
            }
            // Optimales Alter zwischen 16 und 30
            else if ($application->age >= 16 && $application->age <= 30) {
                $score += 10; // Bonus für optimales Alter
            }
            // Alter zwischen 12-15 oder über 30 ist neutral
        }

        // Technische Voraussetzungen bewerten
        if ($application->ram) {
            if ($application->ram >= 16) $score += 10;
            else if ($application->ram >= 8) $score += 5;
        }

        // Begrenze den Score auf 0-100
        return max(0, min(100, $score));
    }

    /**
     * Identifiziert Stärken in der Bewerbung
     *
     * @param Application $application Die zu analysierende Bewerbung
     * @return array Liste der Stärken
     */
    private function identifyStrengths(Application $application): array
    {
        $strengths = [];

        // Vollständigkeit der Bewerbung
        $requiredFields = ['name', 'age', 'gender', 'professions', 'about_you', 'strengths_weaknesses'];
        $filledFields = 0;

        foreach ($requiredFields as $field) {
            if (!empty($application->$field)) {
                $filledFields++;
            }
        }

        $completeness = ($filledFields / count($requiredFields)) * 100;
        if ($completeness >= 80) {
            $strengths[] = 'Vollständige Bewerbung';
        }

        // Ausführliche Texte
        if (!empty($application->about_you) && strlen($application->about_you) > 300) {
            $strengths[] = 'Ausführliche Selbstbeschreibung';
        }

        if (!empty($application->strengths_weaknesses) && strlen($application->strengths_weaknesses) > 200) {
            $strengths[] = 'Gute Reflexion über Stärken und Schwächen';
        }

        // Alter bewerten
        if ($application->age) {
            if ($application->age >= 16 && $application->age <= 30) {
                $strengths[] = 'Optimales Alter für die Mitarbeit';
            }
        }

        // Technische Voraussetzungen
        if ($application->ram && $application->ram >= 16) {
            $strengths[] = 'Gute Hardware-Ausstattung';
        }

        return $strengths;
    }

    /**
     * Identifiziert Schwächen in der Bewerbung
     *
     * @param Application $application Die zu analysierende Bewerbung
     * @return array Liste der Schwächen
     */
    private function identifyWeaknesses(Application $application): array
    {
        $weaknesses = [];

        // Unvollständige Bewerbung
        $requiredFields = ['name', 'age', 'gender', 'professions', 'about_you', 'strengths_weaknesses'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($application->$field)) {
                $missingFields[] = $field;
            }
        }

        if (count($missingFields) > 0) {
            $weaknesses[] = 'Unvollständige Bewerbung';
        }

        // Alter bewerten
        if ($application->age) {
            if ($application->age < 14) {
                $weaknesses[] = 'Bewerber ist zu jung (unter 14 Jahre)';
            } else if ($application->age < 16) {
                $weaknesses[] = 'Bewerber ist relativ jung (unter 16 Jahre)';
            } else if ($application->age > 50) {
                $weaknesses[] = 'Bewerber ist älter als der typische Teilnehmer';
            }
        }

        // Kurze Texte
        if (!empty($application->about_you) && strlen($application->about_you) < 100) {
            $weaknesses[] = 'Zu kurze Selbstbeschreibung';
        }

        if (!empty($application->strengths_weaknesses) && strlen($application->strengths_weaknesses) < 50) {
            $weaknesses[] = 'Zu kurze Reflexion über Stärken und Schwächen';
        }

        // Technische Einschränkungen
        if ($application->ram && $application->ram < 8) {
            $weaknesses[] = 'Geringe Hardware-Ausstattung';
        }

        return $weaknesses;
    }

    /**
     * Generiert eine formatierte Markdown-Antwort für die Vorschau
     *
     * @param Application $application Die zu bearbeitende Bewerbung
     * @param string $status Der Status der Bewerbung (approved, rejected, pending)
     * @param array $selectedReasons Array mit ausgewählten Ablehnungsgründen
     * @param string $customReasons Benutzerdefinierte Ablehnungsgründe
     * @return string Die generierte Markdown-Antwort
     */
    private function generateMarkdownResponse(Application $application, string $status, array $selectedReasons, string $customReasons): string
    {
        Log::debug('Generiere Markdown-Antwort', [
            'application_id' => $application->id,
            'status' => $status,
            'reasons_count' => count($selectedReasons)
        ]);

        // Fehlerhafte Eingaben abfangen und mit sicheren Standardwerten ersetzen
        $roles = $this->getTranslatedRoles($application);
        $name = $application->name ?? 'Name';

        // Status validieren und sicheren Standardwert setzen
        if (!in_array($status, ['approved', 'rejected', 'pending'])) {
            Log::warning('Ungültiger Status bei Markdown-Generierung', ['status' => $status]);
            $status = 'pending';
        }

        $statusText = $status === 'approved' ? 'angenommen 🟢' : ($status === 'rejected' ? 'abgelehnt 🔴' : 'in Bearbeitung ⚪');
        $gender = $application->gender ?? 'male'; // Standardmäßig männlich
        $anrede = ($gender === 'female') ? 'Sehr geehrte Frau' : 'Sehr geehrter Herr';

        // Wenn ein benutzerdefiniertes Pronomen angegeben wurde
        if (!empty($application->pronouns)) {
            $anrede = 'Sehr geehrte*r';
        }

        $markdown = "## Bewerbungsantwort\n\n";
        $markdown .= "| Information | Wert |\n";
        $markdown .= "| --- | --- |\n";
        $markdown .= "| **Bewerbung** | " . $roles . " |\n";
        $markdown .= "| **Username** | " . ($application->discord_id ?? 'Kein Username angegeben') . " |\n";
        $markdown .= "| **Antwort** | " . $statusText . " |\n\n";

        $markdown .= "### Antworttext\n\n";
        $markdown .= "$anrede $name,\n\n";
        $markdown .= "Wir bedanken uns für Ihr Interesse an der Teilnahme an unserem Minecraft-Server und möchten Ihnen mitteilen, dass wir uns als Team dazu entschlossen haben, Ihre Bewerbung ";
        $markdown .= $status === 'approved' ? "anzunehmen.\n\n" : "abzulehnen.\n\n";

        // Inhalte für angenommene Bewerbungen
        if ($status === 'approved') {
            $markdown .= "**Hiermit akzeptieren wir Sie als: " . $roles . ".**\n\n";

            // Personalisierte Nachricht basierend auf den Bewerbungsdaten
            if (!empty($application->strengths_weaknesses)) {
                $markdown .= "Wir haben Ihre Angaben zu Ihren Stärken und Schwächen mit Interesse gelesen und freuen uns auf die Zusammenarbeit mit Ihnen.\n\n";
            }

            // Rollenspezifische Anweisungen hinzufügen
            $markdown .= $this->getRoleSpecificInstructions($roles);

            // Personalisierte Abschlussnachricht
            if (!empty($application->final_words)) {
                $markdown .= "Wir haben Ihre abschließenden Worte zur Kenntnis genommen und freuen uns auf eine erfolgreiche Zusammenarbeit.\n\n";
            }

            // Abschluss
            $markdown .= "Bei Fragen stehen wir Ihnen gerne zur Verfügung.\n\n";
            $markdown .= "Mit freundlichen Grüßen,\n";
            $markdown .= "Das Minewache-Team";
        }
        // Inhalte für abgelehnte Bewerbungen
        else if ($status === 'rejected') {
            $markdown .= $this->getRejectionReasons($selectedReasons, $customReasons);

            // Höflicher Abschluss bei Ablehnung
            $markdown .= "Wir wünschen Ihnen dennoch viel Erfolg bei Ihrer weiteren Suche und bedanken uns für Ihr Interesse an unserem Projekt.\n\n";
            $markdown .= "Mit freundlichen Grüßen,\n";
            $markdown .= "Das Minewache-Team";
        }

        return $markdown;
    }

    /**
     * Generiert eine formatierte Discord-Markdown-Antwort
     *
     * @param Application $application Die zu bearbeitende Bewerbung
     * @param string $status Der Status der Bewerbung (approved, rejected, pending)
     * @param array $selectedReasons Array mit ausgewählten Ablehnungsgründen
     * @param string $customReasons Benutzerdefinierte Ablehnungsgründe
     * @return string Die generierte Discord-Markdown-Antwort
     */
    public function generateDiscordMarkdownResponse(Application $application, string $status, array $selectedReasons, string $customReasons): string
    {
        // Rollen als Array erhalten
        $roleArray = [];
        if (is_array($application->professions)) {
            foreach ($application->professions as $role) {
                $roleArray[] = $this->roleTranslations[$role] ?? $role;
            }
        }

        $roles = implode(', ', $roleArray);
        $name = $application->name ?? 'Name';
        $gender = $application->gender ?? 'male'; // Standardmäßig männlich
        $anrede = ($gender === 'female') ? 'Sehr geehrte Frau' : 'Sehr geehrter Herr';

        // Wenn ein benutzerdefiniertes Pronomen angegeben wurde
        if (!empty($application->pronouns)) {
            $anrede = 'Sehr geehrte*r';
        }

        // Beginne mit der Nachricht
        $markdown = "$anrede $name,\n\n";
        $markdown .= "Wir bedanken uns für Ihr Interesse an der Teilnahme an unserem Minecraft-Server und möchten Ihnen mitteilen, dass wir uns als Team dazu entschlossen haben, Ihre Bewerbung ";
        $markdown .= $status === 'approved' ? "anzunehmen.\n\n" : "abzulehnen.\n\n";

        // Inhalte für angenommene Bewerbungen
        if ($status === 'approved') {
            $markdown .= "Hiermit akzeptieren wir Sie als: $roles.\n\n";

            // Rollenspezifische Anweisungen hinzufügen, aber Duplikate vermeiden
            $addedInstructions = [];

            foreach ($roleArray as $role) {
                // Ersetzen für den Vergleich
                $roleToCheck = str_replace('Schauspieler (no voice)', 'Schauspieler-no-voice', $role);

                foreach ($this->roleInstructions as $rolePattern => $instruction) {
                    if (preg_match('/\b' . preg_quote($rolePattern, '/') . '\b/i', $roleToCheck)) {
                        // Prüfen, ob diese Anweisung bereits hinzugefügt wurde
                        if (!in_array($instruction, $addedInstructions)) {
                            $markdown .= $instruction . "\n\n";
                            $addedInstructions[] = $instruction;
                        }
                        break;
                    }
                }
            }

            // Abschluss
            $markdown .= "Für etwaige Fragen oder weitere Informationen steht Ihnen das Minewache Team jederzeit gerne zur Verfügung.\n\n";
            $markdown .= "**Mit freundlichen Grüßen**\n";
            $markdown .= "**das Minewache Team**\n\n";
            $markdown .= "-# Diese Antwort ist teilweise automatisch generiert.";
        }
        // Inhalte für abgelehnte Bewerbungen
        else if ($status === 'rejected') {
            $markdown .= $this->getRejectionReasons($selectedReasons, $customReasons);

            // Höflicher Abschluss bei Ablehnung
            $markdown .= "Wir wünschen Ihnen dennoch viel Erfolg bei Ihrer weiteren Suche und bedanken uns für Ihr Interesse an unserem Projekt.\n\n";
            $markdown .= "**Mit freundlichen Grüßen**\n";
            $markdown .= "**das Minewache Team**\n\n";
            $markdown .= "-# Diese Antwort ist teilweise automatisch generiert.";
        }

        // Ersetze \n mit doppelten Zeilenumbrüchen für Discord-Kompatibilität, aber nur wenn es nicht bereits ein doppelter Zeilenumbruch ist
        $discordMarkdown = preg_replace('/(?<!\n)\n(?!\n)/', "\n\n", $markdown);

        // Entferne übermäßige Leerzeilen (mehr als 2 aufeinanderfolgende Zeilenumbrüche)
        $discordMarkdown = preg_replace('/\n{3,}/', "\n\n", $discordMarkdown);

        // Stelle sicher, dass die Signatur korrekt formatiert ist
        $discordMarkdown = preg_replace('/\*\*Mit freundlichen Grüßen\*\*\n\n\*\*das Minewache Team\*\*/', "**Mit freundlichen Grüßen**\n**das Minewache Team**", $discordMarkdown);

        return $discordMarkdown;
    }

    /**
     * Generiert einen einfachen Text für die Kopie-Funktion
     *
     * @param Application $application Die zu bearbeitende Bewerbung
     * @param string $status Der Status der Bewerbung (approved, rejected, pending)
     * @param array $selectedReasons Array mit ausgewählten Ablehnungsgründen
     * @param string $customReasons Benutzerdefinierte Ablehnungsgründe
     * @return string Der generierte Plaintext
     */
    private function generatePlainTextResponse(Application $application, string $status, array $selectedReasons, string $customReasons): string
    {
        // Einfachste Lösung: Markdown generieren und dann zu Plaintext konvertieren
        $markdown = $this->generateMarkdownResponse($application, $status, $selectedReasons, $customReasons);
        return $this->convertSingleMarkdownToPlainText($markdown);
    }

    /**
     * Konvertiert die im Modell gespeicherten Rollen in übersetzte Namen
     */
    private function getTranslatedRoles(Application $application): string
    {
        $translatedRoles = [];

        if (is_array($application->professions)) {
            foreach ($application->professions as $role) {
                $translatedRoles[] = $this->roleTranslations[$role] ?? $role;
            }
        }

        return implode(', ', $translatedRoles);
    }

    /**
     * Generiert rollenspezifische Anweisungen basierend auf den zugewiesenen Rollen mit Markdown
     */
    private function getRoleSpecificInstructions(string $roles): string
    {
        $instructions = '';

        // Ersetzen für den Vergleich
        $rolesToCheck = str_replace('Schauspieler (no voice)', 'Schauspieler-no-voice', $roles);

        // Prüfe jede Rolle auf spezifische Anweisungen
        foreach ($this->roleInstructions as $rolePattern => $instruction) {
            if (preg_match('/\b' . preg_quote($rolePattern, '/') . '\b/i', $rolesToCheck)) {
                $instructions .= "**Hinweise für " . $rolePattern . ":**\n" . $instruction . "\n\n";
            }
        }

        return $instructions;
    }

    /**
     * Generiert rollenspezifische Anweisungen als Klartext (ohne Markdown-Links)
     */
    private function getRoleSpecificInstructionsPlainText(string $roles): string
    {
        $instructions = '';
        $roleInstructionsPlain = $this->convertMarkdownToPlainText($this->roleInstructions);

        // Ersetzen für den Vergleich
        $rolesToCheck = str_replace('Schauspieler (no voice)', 'Schauspieler-no-voice', $roles);

        // Prüfe jede Rolle auf spezifische Anweisungen
        foreach ($roleInstructionsPlain as $rolePattern => $instruction) {
            if (preg_match('/\b' . preg_quote($rolePattern, '/') . '\b/i', $rolesToCheck)) {
                $instructions .= "Hinweise für " . $rolePattern . ":\n" . $instruction . "\n\n";
            }
        }

        return $instructions;
    }

    /**
     * Generiert rollenspezifische Anweisungen für Discord-Nachrichten
     *
     * @param string $roles Die Rollen des Bewerbers
     * @return string Die formatierten Anweisungen für Discord
     */
    private function getDiscordRoleSpecificInstructions(string $roles): string
    {
        $instructions = '';

        // Ersetzen für den Vergleich
        $rolesToCheck = str_replace('Schauspieler (no voice)', 'Schauspieler-no-voice', $roles);

        // Prüfe jede Rolle auf spezifische Anweisungen
        foreach ($this->roleInstructions as $rolePattern => $instruction) {
            if (preg_match('/\b' . preg_quote($rolePattern, '/') . '\b/i', $rolesToCheck)) {
                $instructions .= "**Hinweise für $rolePattern:**\n" . $instruction . "\n\n";
            }
        }

        return $instructions;
    }

    /**
     * Erstellt einen Ablehnungstext basierend auf den ausgewählten Gründen mit Markdown
     */
    private function getRejectionReasons(array $selectedReasons, string $customReasons): string
    {
        $reasonsText = [];

        // Vorhandene Gründe
        foreach ($selectedReasons as $reason) {
            if (isset($this->rejectionReasons[$reason])) {
                $reasonsText[] = "**Ablehnungsgrund:** " . $this->rejectionReasons[$reason];
            }
        }

        // Benutzerdefinierte Gründe
        if (!empty($customReasons)) {
            $reasonsText[] = $customReasons;
        }

        return !empty($reasonsText) ? implode("\n\n", $reasonsText) . "\n\n" : '';
    }

    /**
     * Erstellt einen Ablehnungstext basierend auf den ausgewählten Gründen als Klartext
     */
    private function getRejectionReasonsPlainText(array $selectedReasons, string $customReasons): string
    {
        $reasonsText = [];

        // Vorhandene Gründe
        foreach ($selectedReasons as $reason) {
            if (isset($this->rejectionReasons[$reason])) {
                $reasonsText[] = "Ablehnungsgrund: " . $this->rejectionReasons[$reason];
            }
        }

        // Benutzerdefinierte Gründe
        if (!empty($customReasons)) {
            $reasonsText[] = $customReasons;
        }

        return !empty($reasonsText) ? implode("\n\n", $reasonsText) . "\n\n" : '';
    }

    /**
     * Konvertiert Markdown-Links und Formatierungen in einfachen Text
     *
     * @param array $markdownTexts Array mit Markdown-formatierten Texten
     * @return array Array mit konvertierten Texten
     */
    private function convertMarkdownToPlainText(array $markdownTexts): array
    {
        $plainTexts = [];

        foreach ($markdownTexts as $key => $text) {
            // Links umwandeln: [Text](URL) -> Text (URL)
            $plainText = preg_replace('/\[(.*?)\]\((.*?)\)/', '$1 ($2)', $text);

            // Fettschrift entfernen: **Text** -> Text
            $plainText = preg_replace('/\*\*(.*?)\*\*/', '$1', $plainText);

            // Kursivschrift entfernen: *Text* -> Text
            $plainText = preg_replace('/\*(.*?)\*/', '$1', $plainText);

            // Discord-Mentions beibehalten
            $plainTexts[$key] = $plainText;
        }

        return $plainTexts;
    }

    /**
     * Konvertiert einen einzelnen Markdown-Text in Plaintext
     *
     * @param string $markdownText Markdown-formatierter Text
     * @return string Konvertierter Text
     */
    private function convertSingleMarkdownToPlainText(string $markdownText): string
    {
        // Links umwandeln: [Text](URL) -> Text (URL)
        $plainText = preg_replace('/\[(.*?)\]\((.*?)\)/', '$1 ($2)', $markdownText);

        // Fettschrift entfernen: **Text** -> Text
        $plainText = preg_replace('/\*\*(.*?)\*\*/', '$1', $plainText);

        // Kursivschrift entfernen: *Text* -> Text
        $plainText = preg_replace('/\*(.*?)\*/', '$1', $plainText);

        // Überschriften entfernen: ### Text -> Text
        $plainText = preg_replace('/^#+\s+(.*)$/m', '$1', $plainText);

        // Tabellen entfernen (vereinfacht)
        $plainText = preg_replace('/\|[^\n]*\|/m', '', $plainText);
        $plainText = preg_replace('/\|\s*-+\s*\|[^\n]*\|/m', '', $plainText);

        return $plainText;
    }
}
