<?php

namespace App\Http\Controllers;

use App\Models\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MyApplicationController extends Controller
{
    /**
     * Zeigt eine Liste aller Bewerbungen des aktuellen Benutzers.
     */
    public function index()
    {
        $applications = Application::where('user_id', Auth::id())
            ->orderByDesc('created_at')
            ->get();

        return view('applications.my-applications', compact('applications'));
    }

    /**
     * Zeigt die Details einer einzelnen Bewerbung.
     */
    public function show($id)
    {
        $application = $this->getUserApplication($id);
        return view('applications.my-application-detail', compact('application'));
    }

    /**
     * Zeigt das Formular zum Bearbeiten einer Bewerbung.
     */
    public function edit($id)
    {
        $application = $this->getUserApplication($id);

        // Prüfe, ob die Bewerbung bearbeitet werden darf
        if (!$application->editable) {
            return redirect()->route('my.applications.show', $application->id)
                ->with('error', 'Diese Bewerbung kann derzeit nicht bearbeitet werden.');
        }

        return view('applications.my-application-edit', compact('application'));
    }

    /**
     * Aktualisiert eine Bewerbung in der Datenbank.
     */
    public function update(Request $request, $id)
    {
        $application = $this->getUserApplication($id);

        // Prüfe, ob die Bewerbung bearbeitet werden darf
        if (!$application->editable) {
            return redirect()->route('my.applications.show', $application->id)
                ->with('error', 'Diese Bewerbung kann derzeit nicht bearbeitet werden.');
        }

        // Validiere die Eingaben
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'age' => 'required|numeric|min:14|max:120',
            'gender' => 'nullable|string|max:50',
            'pronouns' => 'nullable|string|max:50',
            'professions' => 'required|array|min:1',
            'professions.*' => 'string|max:50|in:actor,builder,designer,developer,other',
            'about_you' => 'required|string|min:50|max:5000',
            'strengths_weaknesses' => 'required|string|min:50|max:5000',
            'final_words' => 'nullable|string|max:1000',
            // Bedingte Felder basierend auf der ausgewählten Profession
            'voice_type' => 'nullable|string|max:255',
            'microphone' => 'nullable|string|max:255',
            'daw' => 'nullable|string|max:255',
            'languages' => 'nullable|string|max:255',
            'ide' => 'nullable|string|max:255',
            'program' => 'nullable|string|max:255',
            'design_style' => 'nullable|string|max:255',
            'favorite_design' => 'nullable|string|max:255',
            'portfolio' => 'nullable|string|url|max:2000',
        ]);

        // Setze den Status zurück auf "pending", da die Bewerbung erneut geprüft werden muss
        $validatedData['status'] = 'pending';

        // Speichere die Änderungen
        $application->update($validatedData);

        return redirect()->route('my.applications')
            ->with('success', 'Deine Bewerbung wurde aktualisiert und wird erneut geprüft.');
    }

    /**
     * Holt eine Bewerbung des aktuellen Benutzers oder gibt eine 404-Fehler zurück.
     */
    private function getUserApplication($id)
    {
        $application = Application::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        return $application;
    }
}
