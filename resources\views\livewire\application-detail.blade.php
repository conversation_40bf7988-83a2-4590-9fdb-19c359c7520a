<div>
    @if(!$application)
        <div class="alert alert-warning shadow-lg mb-6">
            <div>
                <x-heroicon-o-exclamation-triangle class="w-6 h-6"/>
                <span>{{ __('admin.application_not_found') }}</span>
            </div>
            <div class="flex-none">
                <button wire:click="backToList" class="btn btn-sm btn-ghost">{{ __('admin.back_to_overview') }}</button>
            </div>
        </div>
    @else
    <!-- Detailansicht einer Bewerbung -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <button wire:click="backToList" class="btn btn-ghost gap-2">
                <x-heroicon-o-arrow-left class="w-5 h-5"/>
                {{ __('admin.back_to_overview') }}
            </button>

            <div class="flex gap-2">
                <button wire:click="openResponseGenerator"
                        class="btn btn-secondary gap-2">
                    <x-heroicon-o-chat-bubble-left-right class="w-5 h-5"/>
                    {{ __('admin.generate_response') }}
                </button>
                <x-modern-button variant="primary" size="md" wire:click="editApplication"><x-heroicon-o-pencil-square class="w-5 h-5"/>
                    {{ __('admin.edit') }}</x-modern-button>
                <button wire:click="toggleEditability"
                        class="btn {{ $application->editable ? 'btn-error' : 'btn-success' }} gap-2">
                    @if($application->editable)
                        <x-heroicon-o-lock-closed class="w-5 h-5"/>
                        {{ __('admin.lock_editing') }}
                    @else
                        <x-heroicon-o-lock-open class="w-5 h-5"/>
                        {{ __('admin.allow_editing') }}
                    @endif
                </button>
            </div>
        </div>
        <!-- Bewerbungsstatus -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold">{{ $application->name }}</h2>
                <div class="text-base-content/70">
                    {{ __('admin.application_from') }} {{ $application->created_at->format('d.m.Y, H:i') }}
                    Uhr
                </div>
            </div>

            <div class="mt-4 md:mt-0 flex flex-col md:flex-row gap-2 items-end">
                @if($application->status === 'approved')
                    <div class="badge badge-lg badge-success gap-1 p-3">
                        <x-heroicon-o-check-circle class="w-5 h-5"/>
                        {{ __('admin.statistics.approved') }}
                    </div>
                @elseif($application->status === 'rejected')
                    <div class="badge badge-lg badge-error gap-1 p-3">
                        <x-heroicon-o-x-circle class="w-5 h-5"/>
                        {{ __('admin.statistics.rejected') }}
                    </div>
                @else
                    <div class="badge badge-lg badge-warning gap-1 p-3">
                        <x-heroicon-o-clock class="w-5 h-5"/>
                        {{ __('admin.statistics.pending') }}
                    </div>
                @endif

                <div
                    class="badge badge-lg {{ $application->editable ? 'badge-success' : 'badge-neutral' }} gap-1 p-3">
                    @if($application->editable)
                        <x-heroicon-o-lock-open class="w-5 h-5"/>
                        {{ __('admin.editing_allowed') }}
                    @else
                        <x-heroicon-o-lock-closed class="w-5 h-5"/>
                        {{ __('admin.editing_locked') }}
                    @endif
                </div>
            </div>
        </div>

        <!-- Bewerbungsdaten -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Persönliche Daten -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-user-circle class="w-5 h-5 text-primary"/>
                        {{ __('admin.personal_data') }}
                    </h3>
                    <div class="space-y-3 mt-2">
                        <div class="flex justify-between">
                            <span class="font-medium">{{ __('admin.name') }}:</span>
                            <span>{{ $application->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">{{ __('admin.gender') }}:</span>
                            <span>{{ $application->gender }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">{{ __('admin.pronouns') }}:</span>
                            <span>{{ $application->pronouns }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">{{ __('admin.discord_id') }}:</span>
                            <span>{{ $application->discord_id }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tätigkeiten und Fähigkeiten -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-briefcase class="w-5 h-5 text-primary"/>
                        {{ __('admin.professions_abilities') }}
                    </h3>
                    <div class="space-y-3 mt-2">
                        <div>
                            <div class="font-medium mb-1">{{ __('admin.selected_professions') }}:</div>
                            <div class="flex flex-wrap gap-1">
                                @if(is_array($application->professions))
                                    @foreach($application->professions as $profession)
                                        <div class="badge badge-primary">
                                            {{ __('admin.profession.' . $profession) }}
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>

                        @if(isset($application->voice_type))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.voice_type') }}:</span>
                                <span>{{ $application->voice_type }}</span>
                            </div>
                        @endif

                        @if(isset($application->ram))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.ram') }}:</span>
                                <span>{{ $application->ram }} GB</span>
                            </div>
                        @endif

                        @if(isset($application->fps))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.fps') }}:</span>
                                <span>{{ $application->fps }}</span>
                            </div>
                        @endif

                        @if(isset($application->desired_role))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.desired_role') }}:</span>
                                <span>{{ $application->desired_role }}</span>
                            </div>
                        @endif

                        @if(isset($application->languages))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.languages') }}:</span>
                                <span>{{ $application->languages }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Technische Details -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-computer-desktop class="w-5 h-5 text-primary"/>
                        {{ __('admin.technical_details') }}
                    </h3>
                    <div class="space-y-3 mt-2">
                        @if(isset($application->microphone))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.microphone') }}:</span>
                                <span>{{ $application->microphone }}</span>
                            </div>
                        @endif

                        @if(isset($application->daw))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.daw') }}:</span>
                                <span>{{ $application->daw }}</span>
                            </div>
                        @endif

                        @if(isset($application->program))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.software') }}:</span>
                                <span>{{ $application->program }}</span>
                            </div>
                        @endif

                        @if(isset($application->design_style))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.design_style') }}:</span>
                                <span>{{ $application->design_style }}</span>
                            </div>
                        @endif

                        @if(isset($application->favorite_design))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.favorite_design') }}:</span>
                                <span>{{ $application->favorite_design }}</span>
                            </div>
                        @endif

                        @if(isset($application->gpu))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.gpu') }}:</span>
                                <span>{{ $application->gpu }}</span>
                            </div>
                        @endif

                        @if(isset($application->ide))
                            <div class="flex justify-between">
                                <span class="font-medium">{{ __('admin.ide') }}:</span>
                                <span>{{ $application->ide }}</span>
                            </div>
                        @endif

                        @if(is_array($application->checkboxQuestions) && count($application->checkboxQuestions) > 0)
                            <div>
                                <div class="font-medium mb-1">{{ __('admin.confirmations') }}:</div>
                                <div class="space-y-1">
                                    @foreach($application->checkboxQuestions as $question)
                                        <div class="flex items-center">
                                            <x-heroicon-o-check class="w-4 h-4 text-success mr-2"/>
                                            <span class="text-sm">
                                                {{ __('admin.confirmation.' . $question) }}
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Bewerbungstext und Portfolio -->
        <div class="grid grid-cols-1 gap-6 mb-8">
            <!-- Über den Bewerber -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-document-text class="w-5 h-5 text-primary"/>
                        {{ __('admin.about_applicant') }}
                    </h3>

                    <div class="mt-4">
                        <div class="font-medium text-primary">{{ __('admin.about_me') }}</div>
                        <div class="mt-2 p-4 bg-base-100 rounded-lg">
                            {{ $application->about_you ?? __('admin.no_information') }}
                        </div>
                    </div>

                    @if(isset($application->strengths_weaknesses))
                        <div class="mt-4">
                            <div class="font-medium text-primary">{{ __('admin.strengths_weaknesses') }}</div>
                            <div class="mt-2 p-4 bg-base-100 rounded-lg">
                                {{ $application->strengths_weaknesses }}
                            </div>
                        </div>
                    @endif

                    @if(isset($application->final_words))
                        <div class="mt-4">
                            <div class="font-medium text-primary">{{ __('admin.final_words') }}</div>
                            <div class="mt-2 p-4 bg-base-100 rounded-lg">
                                {{ $application->final_words }}
                            </div>
                        </div>
                    @endif

                    @if(isset($application->portfolio))
                        <div class="mt-4">
                            <div class="font-medium text-primary">{{ __('admin.portfolio') }}</div>
                            <div class="mt-2 p-4 bg-base-100 rounded-lg">
                                {{ $application->portfolio }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Kommentar des Teams (falls vorhanden) -->
            @if(isset($application->team_comment))
                <div class="card bg-base-200 shadow-md">
                    <div class="card-body">
                        <h3 class="card-title flex items-center gap-2">
                            <x-heroicon-o-chat-bubble-left-right class="w-5 h-5 text-primary"/>
                            {{ __('admin.internal_notes') }}
                        </h3>
                        <div class="mt-2 p-4 bg-base-100 rounded-lg">
                            {{ $application->team_comment }}
                        </div>
                        @if(isset($application->reviewer_id))
                            <div class="text-sm text-base-content/70 mt-2">
                                {{ __('admin.edited_by') }}
                                : {{ \App\Models\User::find($application->reviewer_id)?->global_name ?? 'Unbekannt' }}
                                @if(isset($application->reviewed_at))
                                    {{ __('admin.on_date') }} {{ $application->reviewed_at->format('d.m.Y, H:i') }}
                                    Uhr
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
    @endif
</div>
