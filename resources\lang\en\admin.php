<?php

return [
    // General admin terms
    'application_overview' => 'Application Overview',
    'search' => 'Search',
    'all_statuses' => 'All Statuses',
    'all_professions' => 'All Professions',
    'reset_filters' => 'Reset Filters',
    'professions' => 'Professions',
    'actions' => 'Actions',
    'years' => 'years',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'back_to_overview' => 'Back to Overview',
    'application_from' => 'Application from',
    'generate_response' => 'Generate Response',
    'lock_editing' => 'Lock Editing',
    'allow_editing' => 'Allow Editing',
    'editing_allowed' => 'Editing Allowed',
    'editing_locked' => 'Editing Locked',
    'application_not_found' => 'Application not found.',
    'error_changing_editability' => 'Error changing editability',
    'no_application_selected' => 'No application selected.',
    'application_editing_disabled' => 'Application editing has been disabled.',
    'application_editing_enabled' => 'Application editing has been enabled.',
    'application_revision_created' => 'A new revision of the application has been created.',
    'enable_editing' => 'Enable Editing',
    'select_editing_mode' => 'Choose how the application should be edited:',
    'edit_directly' => 'Edit Directly',
    'create_revision' => 'Create New Revision',
    'no_permission' => 'You do not have permission for this action.',
    'no_applications_found' => 'No Applications Found',
    'no_applications_matching' => 'No applications matching your search criteria.',
    'permissions_updated_successfully' => 'Permissions for :username successfully updated',
    'permissions_updated_successfully_discord' => 'Permissions for :username successfully updated and synchronized with Discord',
    'error_updating_permissions' => 'Error updating permissions: :error',
    'cancel' => 'Cancel',
    'edit_application' => 'Edit Application',
    'update_status' => 'Update Status',
    'notes_about_applicant' => 'Notes about Applicant',
    'notes_description' => 'Add internal notes about this applicant (only visible to the team)',
    'notes_placeholder' => 'Enter your notes about the applicant here...',
    'save_changes' => 'Save Changes',
    'personal_data' => 'Personal Data',
    'name' => 'Name',
    'age' => 'Age',
    'gender' => 'Gender',
    'pronouns' => 'Pronouns',
    'discord_id' => 'Discord ID',
    'professions_abilities' => 'Professions & Abilities',
    'selected_professions' => 'Selected Professions',
    'voice_type' => 'Voice Type',
    'ram' => 'RAM',
    'fps' => 'FPS',
    'desired_role' => 'Desired Role',
    'languages' => 'Languages',
    'technical_details' => 'Technical Details',
    'microphone' => 'Microphone',
    'daw' => 'DAW',
    'software' => 'Software',
    'design_style' => 'Design Style',
    'favorite_design' => 'Favorite Design',
    'gpu' => 'GPU',
    'created_at' => 'Created At',
    'status' => 'Status',
    'reviewed_at' => 'Reviewed At',
    'ide' => 'IDE',
    'confirmations' => 'Confirmations',
    'about_applicant' => 'About the Applicant',
    'about_me' => 'About Me',
    'no_information' => 'No information provided',
    'strengths_weaknesses' => 'Strengths & Weaknesses',
    'final_words' => 'Final Words',
    'portfolio' => 'Portfolio',
    'internal_notes' => 'Internal Notes',
    'edited_by' => 'Edited by',
    'on_date' => 'on',

    // Profession types
    'profession' => [
        'actor' => 'Actor',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'developer' => 'Developer',
        'musician' => 'Musician',
        'redactor' => 'Redactor',
        'other' => 'Other',
    ],

    // Confirmation types
    'confirmation' => [
        'has_pc' => 'I have a PC',
        'has_java' => 'I have Minecraft Java Edition',
        'has_microphone' => 'I have a working microphone',
        'understands_roleplay' => 'I understand this is a roleplay project',
        'agrees_to_rules' => 'I agree to the server rules',
    ],

    // Statistics
    'statistics' => [
        'total' => 'Total',
        'total_applications' => 'Total Applications',
        'pending' => 'Pending',
        'waiting_for_processing' => 'Waiting for Processing',
        'approved' => 'Approved',
        'approved_applications' => 'Approved Applications',
        'rejected' => 'Rejected',
        'rejected_applications' => 'Rejected Applications',
    ],

    // Response Generator
    'back_to_details' => 'Back to Details',
    'smart_response' => 'Smart Response',
    'generate_response_title' => 'Generate Response',
    'copied_to_clipboard' => 'Response copied to clipboard',
    'application_analysis' => 'Application Analysis',
    'recommendation' => 'Recommendation',
    'recommendation_acceptance' => 'Acceptance',
    'recommendation_rejection' => 'Rejection',
    'quality_rating' => 'Quality Rating',
    'strengths' => 'Strengths',
    'weaknesses' => 'Weaknesses',
    'no_strengths_detected' => 'No particular strengths detected',
    'no_weaknesses_detected' => 'No particular weaknesses detected',
    'change_status' => 'Change Status',
    'response_auto_generated' => 'The response was automatically generated based on the analysis. You can change the status if needed.',
    'accepted' => 'Accepted',
    'rejected' => 'Rejected',
    'recommended' => 'Recommended',
    'rejection_reasons' => 'Rejection Reasons',
    'suggested_reasons_detected' => 'Based on the application, the following rejection reasons were detected:',
    'add_reason' => 'Add',
    'rp_misunderstanding' => 'RP Server Misunderstanding',
    'no_pc' => 'No PC Available',
    'no_pc_minecraft' => 'No PC & No Minecraft Java',
    'insufficient_ram' => 'Insufficient RAM',
    'insufficient_fps' => 'Insufficient FPS',
    'no_microphone' => 'No Microphone',
    'no_daw' => 'No DAW',
    'too_young' => 'Too Young (under 14 years)',
    'applicant_thinks_rp' => 'Applicant thinks we are an RP server',
    'additional_reasons' => 'Additional Reasons',
    'additional_reasons_placeholder' => 'Enter additional reasons for rejection...',
    'generated_response' => 'Generated Response',
    'copy' => 'Copy',
    'preview' => 'Preview',
    'raw_text' => 'Raw Text',
    'no_preview_available' => 'No preview available',
    'response_can_be_copied' => 'The generated response can be copied into Discord as a message. Click "Copy" to copy the text to your clipboard.',
    'cancel_button' => 'Cancel',
    'save_apply_status' => 'Save Status & Apply',

    // Response Generator Messages
    'processing_application_data' => 'Processing application data...',
    'error_processing_application' => 'Error processing application',
    'try_again' => 'Try Again',
];
