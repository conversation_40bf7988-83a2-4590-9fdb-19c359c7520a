[2025-06-25 22:54:42] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:42] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:42] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:45] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:45] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:45] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:45] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:45] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:45] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 22:54:48] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:48] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:48] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:48] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:48] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:48] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:49] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:49] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:49] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:49] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:49] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:49] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:50] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:54:50] local.DEBUG: received heartbeat ack {"response_time":119.39597129821777} 
[2025-06-25 22:54:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:51] local.DEBUG: Discord Bot API: Received request {"path":"/api/ping","method":"GET","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"]}} 
[2025-06-25 22:54:51] local.DEBUG: ResponseGenerator: Discord-Bot-Status geprüft {"status":"online"} 
[2025-06-25 22:54:51] local.INFO: ResponseGenerator: Lade Bewerbung {"application_id":1} 
[2025-06-25 22:54:51] local.INFO: ResponseGenerator: Bewerbung wird analysiert {"application_id":1} 
[2025-06-25 22:54:51] local.DEBUG: Bewerbungsanalyse aus Cache laden oder neu berechnen {"application_id":1,"cache_key":"application_analysis_1_1748892539"} 
[2025-06-25 22:54:51] local.INFO: Cache-Miss: Analysiere Bewerbung {"application_id":1} 
[2025-06-25 22:54:51] local.DEBUG: Ablehnungsgründe berechnen oder aus Cache laden {"application_id":1,"cache_key":"application_rejection_reasons_1_1748892539"} 
[2025-06-25 22:54:51] local.INFO: Cache-Miss: Berechne Ablehnungsgründe für Bewerbung {"application_id":1} 
[2025-06-25 22:54:51] local.DEBUG: Berechnete Ablehnungsgründe {"application_id":1,"reasons_count":0,"reasons":[]} 
[2025-06-25 22:54:51] local.DEBUG: Bewerbungsanalyse abgeschlossen {"application_id":1,"status":"approved","quality":75,"strengths_count":2,"weaknesses_count":1} 
[2025-06-25 22:54:51] local.INFO: ResponseGenerator: Antwort wird generiert {"application_id":1} 
[2025-06-25 22:54:51] local.DEBUG: Bewerbungsanalyse aus Cache laden oder neu berechnen {"application_id":1,"cache_key":"application_analysis_1_1748892539"} 
[2025-06-25 22:54:51] local.INFO: GenerateFullResponse aufgerufen {"application_id":1,"status":"approved","reasons_count":0,"custom_reasons_length":0,"use_smart":true} 
[2025-06-25 22:54:51] local.DEBUG: Generiere Markdown-Antwort {"application_id":1,"status":"approved","reasons_count":0} 
[2025-06-25 22:54:51] local.DEBUG: Generiere Markdown-Antwort {"application_id":1,"status":"approved","reasons_count":0} 
[2025-06-25 22:54:51] local.DEBUG: Ablehnungsgründe berechnen oder aus Cache laden {"application_id":1,"cache_key":"application_rejection_reasons_1_1748892539"} 
[2025-06-25 22:54:51] local.DEBUG: ResponseGenerator: Rendering mit Antwort {"markdown_length":931,"copyable_length":775,"discord_length":0,"processing_state":"success"} 
[2025-06-25 22:54:54] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:54:54] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:54:54] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:54:54] local.DEBUG: Discord Bot API: Received request {"path":"/api/ping","method":"GET","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"]}} 
[2025-06-25 22:54:54] local.DEBUG: ResponseGenerator: Discord-Bot-Status geprüft {"status":"online"} 
[2025-06-25 22:54:55] local.DEBUG: Discord Bot API: Received request {"path":"/api/status","method":"GET","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"]}} 
[2025-06-25 22:54:55] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:54:55] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:54:55] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:00] local.INFO: Discord message retry attempt 2 for application 1  
[2025-06-25 22:55:01] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:55:01] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:01] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:06] local.INFO: Discord message retry attempt 3 for application 1  
[2025-06-25 22:55:08] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:55:08] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:08] local.WARNING: Fehler bei der Bot-Antwort {"status":503,"body":"{\"error\":\"Service Unavailable\",\"message\":\"Discord client not ready\"}","user_id":"536260379098218516"} 
[2025-06-25 22:55:08] local.INFO: Discord message retry attempt 2 for application 1  
[2025-06-25 22:55:09] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:55:09] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:09] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:14] local.INFO: Discord message retry attempt 2 for application 1  
[2025-06-25 22:55:16] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:55:16] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:16] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:21] local.INFO: Discord message retry attempt 3 for application 1  
[2025-06-25 22:55:23] local.DEBUG: Discord Bot API: Received request {"path":"/api/messages/send","method":"POST","query":[],"headers":{"Host":["localhost:3001"],"User-Agent":["GuzzleHttp/7"],"Content-Type":["application/json"],"Authorization":["Bearer Ivn6ctvUld"],"Accept":["application/json"],"X-Request-Source":["DiscordService"],"Content-Length":["690"]}} 
[2025-06-25 22:55:23] local.WARNING: Discord Bot API: Discord client not ready for sending message {"user_id":"536260379098218516"} 
[2025-06-25 22:55:23] local.WARNING: Fehler bei der Bot-Antwort {"status":503,"body":"{\"error\":\"Service Unavailable\",\"message\":\"Discord client not ready\"}","user_id":"536260379098218516"} 
[2025-06-25 22:55:23] local.INFO: Discord message retry attempt 3 for application 1  
[2025-06-25 22:55:25] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":"536260379098218516","exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Livewire\\ResponseGenerator.php:638)
[stacktrace]
#0 {main}
"} 
[2025-06-25 22:55:31] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:55:31] local.DEBUG: received heartbeat ack {"response_time":118.29304695129395} 
[2025-06-25 22:55:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:55:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:55:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:55:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:55:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 22:56:13] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:56:13] local.DEBUG: received heartbeat ack {"response_time":122.73883819580078} 
[2025-06-25 22:56:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:56:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:56:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:56:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:56:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:56:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:56:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:56:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:56:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:56:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 22:56:54] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:56:54] local.DEBUG: received heartbeat ack {"response_time":111.16504669189453} 
[2025-06-25 22:57:35] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:57:35] local.DEBUG: received heartbeat ack {"response_time":148.50997924804688} 
[2025-06-25 22:57:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:57:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:57:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:57:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:57:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:57:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 22:58:16] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:58:16] local.DEBUG: received heartbeat ack {"response_time":111.25302314758301} 
[2025-06-25 22:58:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:58:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:58:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:58:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:58:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:58:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:58:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 22:58:58] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:58:58] local.DEBUG: received heartbeat ack {"response_time":152.86993980407715} 
[2025-06-25 22:59:39] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 22:59:39] local.DEBUG: received heartbeat ack {"response_time":110.0759506225586} 
[2025-06-25 22:59:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:59:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 22:59:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:59:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:59:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 22:59:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 22:59:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:00:20] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:00:20] local.DEBUG: received heartbeat ack {"response_time":137.16912269592285} 
[2025-06-25 23:00:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:00:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:00:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:00:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:00:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:00:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:00:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:01:01] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:01:01] local.DEBUG: received heartbeat ack {"response_time":111.38391494750977} 
[2025-06-25 23:01:43] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:01:43] local.DEBUG: received heartbeat ack {"response_time":129.64606285095215} 
[2025-06-25 23:01:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:01:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:01:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:01:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:01:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:02:24] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:02:24] local.DEBUG: received heartbeat ack {"response_time":115.68188667297363} 
[2025-06-25 23:02:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:02:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:02:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:02:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:02:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:02:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:03:05] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:03:05] local.DEBUG: received heartbeat ack {"response_time":138.12994956970215} 
[2025-06-25 23:03:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:03:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:03:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:03:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:03:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:03:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:03:46] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:03:46] local.DEBUG: received heartbeat ack {"response_time":109.85422134399414} 
[2025-06-25 23:04:28] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:04:28] local.DEBUG: received heartbeat ack {"response_time":131.9289207458496} 
[2025-06-25 23:04:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:04:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:04:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:04:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:04:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:04:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:04:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:05:09] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:05:09] local.DEBUG: received heartbeat ack {"response_time":109.76910591125488} 
[2025-06-25 23:05:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:05:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:05:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:05:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:05:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:05:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:05:50] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:05:50] local.DEBUG: received heartbeat ack {"response_time":123.68202209472656} 
[2025-06-25 23:06:31] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:06:32] local.DEBUG: received heartbeat ack {"response_time":112.69402503967285} 
[2025-06-25 23:06:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:06:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:06:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:06:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:06:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:06:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:06:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:06:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:06:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:07:13] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:07:13] local.DEBUG: received heartbeat ack {"response_time":113.11483383178711} 
[2025-06-25 23:07:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:07:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:07:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:07:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:07:54] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:07:54] local.DEBUG: received heartbeat ack {"response_time":112.98584938049316} 
[2025-06-25 23:08:06] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:06] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:06] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:09] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:09] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:09] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:09] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:09] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:09] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:09] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:09] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:10] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:10] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:10] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:10] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:10] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:10] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:29] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:29] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:29] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:32] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:32] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:32] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:33] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:33] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:33] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:33] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:33] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:33] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:33] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:33] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:33] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:35] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:08:35] local.DEBUG: received heartbeat ack {"response_time":112.0760440826416} 
[2025-06-25 23:08:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:08:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:08:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:08:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:09:16] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:09:17] local.DEBUG: received heartbeat ack {"response_time":110.35394668579102} 
[2025-06-25 23:09:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:09:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:09:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:09:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:09:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:09:58] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:09:58] local.DEBUG: received heartbeat ack {"response_time":112.85901069641113} 
[2025-06-25 23:10:39] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:10:39] local.DEBUG: received heartbeat ack {"response_time":109.44819450378418} 
[2025-06-25 23:10:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:10:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:10:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:10:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:10:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:10:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:10:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:11:20] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:11:20] local.DEBUG: received heartbeat ack {"response_time":109.52591896057129} 
[2025-06-25 23:11:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:11:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:11:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:11:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:12:01] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:12:02] local.DEBUG: received heartbeat ack {"response_time":110.46600341796875} 
[2025-06-25 23:12:43] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:12:43] local.DEBUG: received heartbeat ack {"response_time":109.59005355834961} 
[2025-06-25 23:12:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:12:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:12:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:12:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:13:24] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:13:24] local.DEBUG: received heartbeat ack {"response_time":109.86995697021484} 
[2025-06-25 23:13:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:13:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:13:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:13:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:13:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:13:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:13:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:14:05] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:14:05] local.DEBUG: received heartbeat ack {"response_time":112.72692680358887} 
[2025-06-25 23:14:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:14:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:14:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:14:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:14:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:14:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:14:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:14:47] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:14:47] local.DEBUG: received heartbeat ack {"response_time":119.73309516906738} 
[2025-06-25 23:15:28] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:15:28] local.DEBUG: received heartbeat ack {"response_time":121.26398086547852} 
[2025-06-25 23:15:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:15:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:15:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:15:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:15:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:15:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:15:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:16:09] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:16:09] local.DEBUG: received heartbeat ack {"response_time":131.86001777648926} 
[2025-06-25 23:16:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:16:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:16:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:16:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:16:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:16:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:16:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:16:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:16:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:16:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:16:50] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:16:50] local.DEBUG: received heartbeat ack {"response_time":110.82792282104492} 
[2025-06-25 23:17:32] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:17:32] local.DEBUG: received heartbeat ack {"response_time":131.5298080444336} 
[2025-06-25 23:17:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:17:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:17:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:17:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:17:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:17:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:17:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:18:13] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:18:13] local.DEBUG: received heartbeat ack {"response_time":116.27507209777832} 
[2025-06-25 23:18:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:18:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:18:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:18:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:18:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:18:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:18:54] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:18:54] local.DEBUG: received heartbeat ack {"response_time":110.90493202209473} 
[2025-06-25 23:19:35] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:19:35] local.DEBUG: received heartbeat ack {"response_time":117.8278923034668} 
[2025-06-25 23:19:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:19:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:19:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:19:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:19:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:19:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:19:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:20:17] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:20:17] local.DEBUG: received heartbeat ack {"response_time":120.36585807800293} 
[2025-06-25 23:20:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:20:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:20:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:20:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:20:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:20:46] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:20:58] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:20:58] local.DEBUG: received heartbeat ack {"response_time":111.43016815185547} 
[2025-06-25 23:21:39] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:21:39] local.DEBUG: received heartbeat ack {"response_time":109.45391654968262} 
[2025-06-25 23:21:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:21:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:21:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:21:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:21:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:21:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:21:46] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:22:20] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:22:20] local.DEBUG: received heartbeat ack {"response_time":115.82016944885254} 
[2025-06-25 23:22:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:22:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:22:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:22:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:22:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:22:47] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:23:02] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:23:02] local.DEBUG: received heartbeat ack {"response_time":109.1761589050293} 
[2025-06-25 23:23:43] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:23:43] local.DEBUG: received heartbeat ack {"response_time":112.77389526367188} 
[2025-06-25 23:23:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:23:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:23:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:23:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:23:47] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:24:24] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:24:24] local.DEBUG: received heartbeat ack {"response_time":109.26008224487305} 
[2025-06-25 23:24:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:24:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:24:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:24:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:24:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:24:47] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:25:05] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:25:05] local.DEBUG: received heartbeat ack {"response_time":110.86106300354004} 
[2025-06-25 23:25:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:25:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:25:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:25:47] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:25:47] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:25:47] local.DEBUG: received heartbeat ack {"response_time":130.17511367797852} 
[2025-06-25 23:26:28] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:26:28] local.DEBUG: received heartbeat ack {"response_time":121.87790870666504} 
[2025-06-25 23:26:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:26:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:26:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:26:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:26:43] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:26:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:26:43] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:26:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:26:43] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:26:47] local.DEBUG: resetting payload count {"count":2} 
[2025-06-25 23:27:09] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:27:09] local.DEBUG: received heartbeat ack {"response_time":109.9240779876709} 
[2025-06-25 23:27:44] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:27:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:27:44] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:27:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:27:44] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:27:47] local.DEBUG: resetting payload count {"count":1} 
[2025-06-25 23:27:50] local.DEBUG: sending heartbeat {"seq":11} 
[2025-06-25 23:27:50] local.DEBUG: received heartbeat ack {"response_time":110.58902740478516} 
[2025-06-25 23:27:56] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:27:56] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:27:56] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:27:56] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:27:56] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:27:56] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:27:56] local.INFO: CustomBroadcastingServiceProvider booted  
d configuration  
[2025-06-25 23:27:56] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:27:56] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:27:56] local.INFO: QueueWorkReverb: Setting broadcast driver to reverb {"broadcast_driver":"reverb"} 
[2025-06-25 23:27:56] local.INFO: Discord Bot: HTTP API Server initialized on port 3001  
[2025-06-25 23:27:56] local.DEBUG: Initializing DiscordPHP v10.0.0 (DiscordPHP-Http: v10.3.0 & Gateway: v10) on PHP 8.4.0  
[2025-06-25 23:27:56] local.DEBUG: BUCKET getapplications/@me queued REQ GET applications/@me  
[2025-06-25 23:27:56] local.DEBUG: BUCKET getgateway/bot queued REQ GET gateway/bot  
[2025-06-25 23:27:57] local.DEBUG: REQ GET gateway/bot successful  
[2025-06-25 23:27:57] local.DEBUG: http not checking {"waiting":1,"empty":true} 
[2025-06-25 23:27:57] local.INFO: gateway retrieved and set {"gateway":"wss://gateway.discord.gg/?v=10&encoding=json&compress=zlib-stream","session":{"max_concurrency":1,"remaining":994,"reset_after":78119028,"total":1000}} 
[2025-06-25 23:27:57] local.INFO: starting connection to websocket {"gateway":"wss://gateway.discord.gg/?v=10&encoding=json&compress=zlib-stream"} 
[2025-06-25 23:27:57] local.DEBUG: REQ GET applications/@me successful  
[2025-06-25 23:27:57] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-06-25 23:27:57] local.INFO: websocket connection has been created  
[2025-06-25 23:27:57] local.INFO: received hello  
[2025-06-25 23:27:57] local.DEBUG: sending heartbeat {"seq":null} 
[2025-06-25 23:27:57] local.INFO: heartbeat timer initialized {"interval":41250.0} 
[2025-06-25 23:27:57] local.INFO: identifying {"payload":{"op":2,"d":{"token":"xxxxx","properties":{"os":"WINNT","browser":"DiscordBot (https://github.com/discord-php/DiscordPHP-HTTP, v10.3.0)","device":"DiscordBot (https://github.com/discord-php/DiscordPHP-HTTP, v10.3.0)","referrer":"https://github.com/discord-php/DiscordPHP","referring_domain":"https://github.com/discord-php/DiscordPHP"},"compress":true,"intents":37379},"s":null,"t":null}} 
[2025-06-25 23:27:57] local.DEBUG: received heartbeat ack {"response_time":108.46996307373047} 
[2025-06-25 23:27:57] local.DEBUG: ready packet received  
[2025-06-25 23:27:57] local.DEBUG: resume_gateway_url received {"url":"wss://gateway-us-east1-c.discord.gg"} 
[2025-06-25 23:27:57] local.DEBUG: discord trace received {"trace":["[\"gateway-prd-us-east1-c-891c\",{\"micros\":115125,\"calls\":[\"id_created\",{\"micros\":379,\"calls\":[]},\"session_lookup_time\",{\"micros\":2215,\"calls\":[]},\"session_lookup_finished\",{\"micros\":18,\"calls\":[]},\"discord-sessions-prd-2-149\",{\"micros\":112061,\"calls\":[\"start_session\",{\"micros\":92802,\"calls\":[\"discord-api-rpc-745b45cfc9-l24qc\",{\"micros\":83047,\"calls\":[\"get_user\",{\"micros\":18699},\"get_guilds\",{\"micros\":3947},\"send_scheduled_deletion_message\",{\"micros\":16},\"guild_join_requests\",{\"micros\":3},\"authorized_ip_coro\",{\"micros\":12},\"pending_payments\",{\"micros\":1293},\"apex_experiments\",{\"micros\":11},\"user_activities\",{\"micros\":4},\"played_application_ids\",{\"micros\":2}]}]},\"starting_guild_connect\",{\"micros\":48,\"calls\":[]},\"presence_started\",{\"micros\":8362,\"calls\":[]},\"guilds_started\",{\"micros\":99,\"calls\":[]},\"lobbies_started\",{\"micros\":1,\"calls\":[]},\"guilds_connect\",{\"micros\":1,\"calls\":[]},\"presence_connect\",{\"micros\":10727,\"calls\":[]},\"connect_finished\",{\"micros\":10731,\"calls\":[]},\"build_ready\",{\"micros\":16,\"calls\":[]},\"clean_ready\",{\"micros\":0,\"calls\":[]},\"optimize_ready\",{\"micros\":1,\"calls\":[]},\"split_ready\",{\"micros\":0,\"calls\":[]}]}]}]"]} 
[2025-06-25 23:27:57] local.DEBUG: client created and session id stored {"session_id":"5fcfb432afa24d22178260e6e1ceadfe","user":{"id":"1172260825734598740","username":"MWTest","discriminator":"4565","global_name":null,"avatar":"https://cdn.discordapp.com/avatars/1172260825734598740/52d86cb7985805a0895762a00cc2094a.webp?size=1024","bot":true,"system":null,"mfa_enabled":true,"banner":null,"accent_color":null,"locale":null,"verified":true,"email":null,"flags":0,"premium_type":null,"public_flags":null,"avatar_decoration":null,"primaryGuild":null,"collectibles":null}} 
[2025-06-25 23:27:57] local.INFO: stored guilds {"count":0,"unavailable":3} 
[2025-06-25 23:27:57] local.DEBUG: guild available {"guild":"932273400188129311","unavailable":3} 
[2025-06-25 23:27:57] local.DEBUG: guild available {"guild":"1180290677599387729","unavailable":2} 
[2025-06-25 23:27:57] local.DEBUG: guild available {"guild":"1335685796979671111","unavailable":1} 
[2025-06-25 23:27:57] local.INFO: all guilds are now available {"count":3} 
[2025-06-25 23:27:57] local.INFO: set up chunking, checking for chunks every 5 seconds  
[2025-06-25 23:27:57] local.DEBUG: sending 1 chunks with 3 large guilds overall  
[2025-06-25 23:27:57] local.DEBUG: sending chunk with 3 large guilds  
[2025-06-25 23:27:57] local.DEBUG: received guild member chunk {"guild_id":"932273400188129311","guild_name":"Musik","chunk_count":9,"member_collection":1,"member_count":9,"progress":[1,1]} 
[2025-06-25 23:27:57] local.DEBUG: parsed 8 members (skipped 0) {"repository_count":9,"actual_count":9} 
[2025-06-25 23:27:57] local.DEBUG: all users have been loaded {"guild":"932273400188129311","member_collection":9,"member_count":9} 
[2025-06-25 23:27:58] local.DEBUG: received guild member chunk {"guild_id":"1180290677599387729","guild_name":"Bot Test","chunk_count":32,"member_collection":1,"member_count":32,"progress":[1,1]} 
[2025-06-25 23:27:58] local.DEBUG: parsed 31 members (skipped 0) {"repository_count":32,"actual_count":32} 
[2025-06-25 23:27:58] local.DEBUG: all users have been loaded {"guild":"1180290677599387729","member_collection":32,"member_count":32} 
[2025-06-25 23:27:58] local.DEBUG: received guild member chunk {"guild_id":"1335685796979671111","guild_name":"MW Bot Test","chunk_count":10,"member_collection":1,"member_count":10,"progress":[1,1]} 
[2025-06-25 23:27:58] local.DEBUG: parsed 9 members (skipped 0) {"repository_count":10,"actual_count":10} 
[2025-06-25 23:27:58] local.DEBUG: all users have been loaded {"guild":"1335685796979671111","member_collection":10,"member_count":10} 
[2025-06-25 23:27:58] local.INFO: client is ready  
[2025-06-25 23:27:58] local.INFO: The 'ready' event is deprecated and will be removed in a future version of DiscordPHP. Please use 'init' instead.  
[2025-06-25 23:27:58] local.INFO: Discord Bot: Connected as   
[2025-06-25 23:27:58] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-06-25 23:27:58] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-06-25 23:27:58] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-06-25 23:27:58] local.INFO: Discord Bot: HTTP API Server started on port 3001  
[2025-06-25 23:27:58] local.INFO: Discord Bot: Now fully ready and accepting API requests.  
[2025-06-25 23:27:59] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-06-25 23:27:59] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-06-25 23:27:59] local.INFO: Global command /support (with reconstruct) registered/updated.  
[2025-06-25 23:27:59] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-06-25 23:27:59] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-06-25 23:27:59] local.INFO: /ping registered.  
[2025-06-25 23:27:59] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-06-25 23:27:59] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-06-25 23:27:59] local.INFO: /setup-tickets registered.  
[2025-06-25 23:28:13] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:13] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:13] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:17] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:17] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:17] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:17] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:17] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:17] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:19] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:19] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:19] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:19] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:19] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:19] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:20] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:20] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:20] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:20] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:20] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:20] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:22] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:22] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:22] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:22] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-06-25 23:28:22] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-06-25 23:28:22] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-06-25 23:28:22] local.ERROR: syntax error, unexpected token "<<" {"view":{"view":"C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\resources\\views\\livewire\\application-manager.blade.php","data":[]},"userId":"536260379098218516","exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected token \"<<\" at C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Livewire\\ResponseGenerator.php:628)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('C:\\\\Users\\\\<USER>\\Autoload\\ClassLoader->loadClass('App\\\\Livewire\\\\Re...')
#2 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(97): class_exists('App\\\\Livewire\\\\Re...')
#3 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('response-genera...')
#4 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('response-genera...', NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('response-genera...')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('response-genera...', Array, 'lw-1196602549-3')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\resources\\views\\livewire\\application-manager.blade.php(31): Livewire\\LivewireManager->mount('response-genera...', Array, 'lw-1196602549-3', Array, Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\ApplicationManager->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\ApplicationManager), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\ApplicationManager))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#23 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\SetLocale.php(27): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\DetectLanguage.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\DetectLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\"<<\" at C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Livewire\\ResponseGenerator.php:628)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('C:\\\\Users\\\\<USER>\\Autoload\\ClassLoader->loadClass('App\\\\Livewire\\\\Re...')
#2 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(97): class_exists('App\\\\Livewire\\\\Re...')
#3 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('response-genera...')
#4 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('response-genera...', NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('response-genera...')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('response-genera...', Array, 'lw-1196602549-3')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\storage\\framework\\views\\0edb082a0d487062e566fc2b39bd1729.php(120): Livewire\\LivewireManager->mount('response-genera...', Array, 'lw-1196602549-3', Array, Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\ApplicationManager->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\ApplicationManager), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\ApplicationManager))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#23 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\SetLocale.php(27): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\DetectLanguage.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\DetectLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\resources\\views\\livewire\\application-manager.blade.php","data":[]},"userId":"536260379098218516","exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected token \"<<\" at C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Livewire\\ResponseGenerator.php:628)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('C:\\\\Users\\\\<USER>\\Autoload\\ClassLoader->loadClass('App\\\\Livewire\\\\Re...')
#2 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(97): class_exists('App\\\\Livewire\\\\Re...')
#3 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('response-genera...')
#4 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('response-genera...', NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('response-genera...')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('response-genera...', Array, 'lw-1196602549-3')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\resources\\views\\livewire\\application-manager.blade.php(31): Livewire\\LivewireManager->mount('response-genera...', Array, 'lw-1196602549-3', Array, Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\ApplicationManager->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\ApplicationManager), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\ApplicationManager))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#23 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\SetLocale.php(27): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\DetectLanguage.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\DetectLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\"<<\" at C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Livewire\\ResponseGenerator.php:628)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('C:\\\\Users\\\\<USER>\\Autoload\\ClassLoader->loadClass('App\\\\Livewire\\\\Re...')
#2 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(97): class_exists('App\\\\Livewire\\\\Re...')
#3 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php(25): Livewire\\Mechanisms\\ComponentRegistry->getNameAndClass('response-genera...')
#4 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(58): Livewire\\Mechanisms\\ComponentRegistry->new('response-genera...', NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(43): Livewire\\LivewireManager->new('response-genera...')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('response-genera...', Array, 'lw-1196602549-3')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\storage\\framework\\views\\0edb082a0d487062e566fc2b39bd1729.php(120): Livewire\\LivewireManager->mount('response-genera...', Array, 'lw-1196602549-3', Array, Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Livewire\\ApplicationManager->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\Users\\Ferdmusic\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Livewire\\ApplicationManager), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\ApplicationManager))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#23 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\SetLocale.php(27): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\app\\Http\\Middleware\\DetectLanguage.php(24): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\DetectLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\GitHub\\minewache-website\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\"gateway-prd-us-east1-c-0qln\",{\"micros\":159446,\"calls\":[\"id_created\",{\"micros\":411,\"calls\":[]},\"session_lookup_time\",{\"micros\":304,\"calls\":[]},\"session_lookup_finished\",{\"micros\":13,\"calls\":[]},\"discord-sessions-prd-2-95\",{\"micros\":158379,\"calls\":[\"start_session\",{\"micros\":136675,\"calls\":[\"discord-api-prd-canary-rpc-785dc96cfb-xz5mb\",{\"micros\":85813,\"calls\":[\"get_user\",{\"micros\":33348},\"get_guilds\",{\"micros\":5059},\"send_scheduled_deletion_message\",{\"micros\":17},\"guild_join_requests\",{\"micros\":353},\"authorized_ip_coro\",{\"micros\":19},\"pending_payments\",{\"micros\":5513},\"apex_experiments\",{\"micros\":27297},\"user_activities\",{\"micros\":6},\"played_application_ids\",{\"micros\":4}]}]},\"starting_guild_connect\",{\"micros\":56,\"calls\":[]},\"presence_started\",{\"micros\":14572,\"calls\":[]},\"guilds_started\",{\"micros\":82,\"calls\":[]},\"lobbies_started\",{\"micros\":1,\"calls\":[]},\"guilds_connect\",{\"micros\":1,\"calls\":[]},\"presence_connect\",{\"micros\":6975,\"calls\":[]},\"connect_finished\",{\"micros\":6980,\"calls\":[]},\"build_ready\",{\"micros\":11,\"calls\":[]},\"clean_ready\",{\"micros\":1,\"calls\":[]},\"optimize_ready\",{\"micros\":0,\"calls\":[]},\"split_ready\",{\"micros\":0,\"calls\":[]}]}]}]"]} 
[2025-07-04 23:34:15] local.DEBUG: client created and session id stored {"session_id":"6eff3b626e11f0b435764ce5ac6764be","user":{"id":"1172260825734598740","username":"MWTest","discriminator":"4565","global_name":null,"avatar":"https://cdn.discordapp.com/avatars/1172260825734598740/52d86cb7985805a0895762a00cc2094a.webp?size=1024","bot":true,"system":null,"mfa_enabled":true,"banner":null,"accent_color":null,"locale":null,"verified":true,"email":null,"flags":0,"premium_type":null,"public_flags":null,"avatar_decoration":null,"primaryGuild":null,"collectibles":null}} 
[2025-07-04 23:34:15] local.INFO: stored guilds {"count":0,"unavailable":3} 
[2025-07-04 23:34:15] local.DEBUG: guild available {"guild":"1180290677599387729","unavailable":3} 
[2025-07-04 23:34:15] local.DEBUG: guild available {"guild":"1335685796979671111","unavailable":2} 
[2025-07-04 23:34:15] local.DEBUG: guild available {"guild":"932273400188129311","unavailable":1} 
[2025-07-04 23:34:15] local.INFO: all guilds are now available {"count":3} 
[2025-07-04 23:34:15] local.INFO: set up chunking, checking for chunks every 5 seconds  
[2025-07-04 23:34:15] local.DEBUG: sending 1 chunks with 3 large guilds overall  
[2025-07-04 23:34:15] local.DEBUG: sending chunk with 3 large guilds  
[2025-07-04 23:34:15] local.DEBUG: received guild member chunk {"guild_id":"1180290677599387729","guild_name":"Bot Test","chunk_count":32,"member_collection":1,"member_count":32,"progress":[1,1]} 
[2025-07-04 23:34:15] local.DEBUG: parsed 31 members (skipped 0) {"repository_count":32,"actual_count":32} 
[2025-07-04 23:34:15] local.DEBUG: all users have been loaded {"guild":"1180290677599387729","member_collection":32,"member_count":32} 
[2025-07-04 23:34:15] local.DEBUG: received guild member chunk {"guild_id":"1335685796979671111","guild_name":"MW Bot Test","chunk_count":10,"member_collection":1,"member_count":10,"progress":[1,1]} 
[2025-07-04 23:34:15] local.DEBUG: parsed 9 members (skipped 0) {"repository_count":10,"actual_count":10} 
[2025-07-04 23:34:15] local.DEBUG: all users have been loaded {"guild":"1335685796979671111","member_collection":10,"member_count":10} 
[2025-07-04 23:34:16] local.DEBUG: received guild member chunk {"guild_id":"932273400188129311","guild_name":"Musik","chunk_count":9,"member_collection":1,"member_count":9,"progress":[1,1]} 
[2025-07-04 23:34:16] local.DEBUG: parsed 8 members (skipped 0) {"repository_count":9,"actual_count":9} 
[2025-07-04 23:34:16] local.DEBUG: all users have been loaded {"guild":"932273400188129311","member_collection":9,"member_count":9} 
[2025-07-04 23:34:16] local.INFO: client is ready  
[2025-07-04 23:34:16] local.INFO: The 'ready' event is deprecated and will be removed in a future version of DiscordPHP. Please use 'init' instead.  
[2025-07-04 23:34:16] local.INFO: Discord Bot: Connected as   
[2025-07-04 23:34:16] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-07-04 23:34:16] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-07-04 23:34:16] local.DEBUG: BUCKET postapplications/:application_id/commands queued REQ POST applications/1172260825734598740/commands  
[2025-07-04 23:34:16] local.INFO: Discord Bot: HTTP API Server started on port 3001  
[2025-07-04 23:34:16] local.INFO: Discord Bot: Now fully ready and accepting API requests.  
[2025-07-04 23:34:16] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-07-04 23:34:16] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-07-04 23:34:16] local.INFO: Global command /support (with reconstruct) registered/updated.  
[2025-07-04 23:34:16] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-07-04 23:34:16] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-07-04 23:34:16] local.INFO: /ping registered.  
[2025-07-04 23:34:16] local.DEBUG: REQ POST applications/1172260825734598740/commands successful  
[2025-07-04 23:34:16] local.DEBUG: http not checking {"waiting":0,"empty":true} 
[2025-07-04 23:34:16] local.INFO: /setup-tickets registered.  
[2025-07-04 23:34:32] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:34:32] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:34:32] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:34:32] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:34:32] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:34:32] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:34:48] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:34:48] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:34:48] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:34:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:34:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:34:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:34:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:34:55] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:34:56] local.DEBUG: received heartbeat ack {"response_time":120.87202072143555} 
[2025-07-04 23:35:14] local.DEBUG: resetting payload count {"count":7} 
[2025-07-04 23:35:37] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:35:37] local.DEBUG: received heartbeat ack {"response_time":114.71676826477051} 
[2025-07-04 23:35:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:35:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:35:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:35:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:35:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:36:14] local.DEBUG: resetting payload count {"count":1} 
[2025-07-04 23:36:18] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:36:18] local.DEBUG: received heartbeat ack {"response_time":125.66089630126953} 
[2025-07-04 23:36:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:36:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:36:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:36:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:36:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:36:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:36:59] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:36:59] local.DEBUG: received heartbeat ack {"response_time":116.33491516113281} 
[2025-07-04 23:37:04] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:37:04] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:04] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:37:05] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:37:05] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:05] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:37:14] local.DEBUG: resetting payload count {"count":2} 
[2025-07-04 23:37:25] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:37:25] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:25] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:37:26] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:37:26] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:26] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:37:40] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:37:41] local.DEBUG: received heartbeat ack {"response_time":125.71501731872559} 
[2025-07-04 23:37:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:37:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:37:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:37:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:38:08] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:38:08] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:38:08] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:38:08] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:38:08] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:38:08] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:38:14] local.DEBUG: resetting payload count {"count":1} 
[2025-07-04 23:38:22] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:38:22] local.DEBUG: received heartbeat ack {"response_time":120.50318717956543} 
[2025-07-04 23:38:39] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:38:39] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:38:39] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:38:40] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:38:40] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:38:40] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:38:51] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:38:51] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:38:51] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:39:03] local.DEBUG: sending heartbeat {"seq":7} 
[2025-07-04 23:39:03] local.DEBUG: received heartbeat ack {"response_time":118.56508255004883} 
[2025-07-04 23:39:11] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:39:11] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:39:11] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:39:11] local.INFO: Extending BroadcastManager to use Reverb by default  
[2025-07-04 23:39:11] local.INFO: BroadcastServiceProvider booted with updated configuration  
[2025-07-04 23:39:11] local.INFO: CustomBroadcastingServiceProvider booted  
[2025-07-04 23:39:14] local.DEBUG: resetting payload count {"count":2} 
