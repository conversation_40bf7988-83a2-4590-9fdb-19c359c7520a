<?php

return [
    // Application Manager
    'application_overview' => 'Bewerbungsübersicht',
    'statistics' => [
        'total' => 'Gesamt',
        'total_applications' => 'Bewerbungen insgesamt',
        'pending' => 'Ausstehend',
        'waiting_for_process+ing' => 'Warten auf Bearbeitung',
        'approved' => 'Angenommen',
        'approved_applications' => 'Akzeptierte Bewerbungen',
        'rejected' => 'Abgelehnt',
        'rejected_applications' => 'Abgelehnte Bewerbungen',
    ],

    // Filters
    'search' => 'Name oder Beschreibung durchsuchen...',
    'all_statuses' => 'Alle Status',
    'all_professions' => 'Alle Tätigkeiten',
    'reset_filters' => 'Filter zurücksetzen',

    // Table Headers
    'date' => 'Datum',
    'name' => 'Name',
    'professions' => 'Tätigkeiten',
    'status' => 'Status',
    'actions' => 'Aktionen',
    'created_at' => 'Erstellt am',
    'reviewed_at' => 'Bearbeitet am',
    'permissions_updated_successfully' => 'Berechtigungen für :username erfolgreich aktualisiert',
    'permissions_updated_successfully_discord' => 'Berechtigungen für :username erfolgreich aktualisiert und mit Discord synchronisiert',
    'error_updating_permissions' => 'Fehler beim Aktualisieren der Berechtigungen: :error',

    // Table Actions
    'view_details' => 'Details anzeigen',
    'edit' => 'Bearbeiten',

    // Empty States
    'no_applications_found' => 'Keine Bewerbungen gefunden',
    'no_applications_matching' => 'Es wurden keine Bewerbungen gefunden, die deinen Filterkriterien entsprechen.',

    // Detail View
    'back_to_overview' => 'Zurück zur Übersicht',
    'generate_response' => 'Antwort generieren',
    'application_from' => 'Bewerbung vom',
    'edit_application' => 'Bewerbung bearbeiten',
    'allow_editing' => 'Bearbeitung freigeben',
    'lock_editing' => 'Bearbeitung sperren',
    'editing_allowed' => 'Bearbeitung freigegeben',
    'editing_locked' => 'Bearbeitung gesperrt',
    'application_not_found' => 'Bewerbung nicht gefunden.',
    'error_changing_editability' => 'Fehler beim Ändern der Bearbeitbarkeit',
    'no_application_selected' => 'Keine Bewerbung ausgewählt.',
    'application_editing_disabled' => 'Die Bearbeitung der Bewerbung wurde gesperrt.',
    'application_editing_enabled' => 'Die Bearbeitung der Bewerbung wurde freigegeben.',
    'application_revision_created' => 'Eine neue Revision der Bewerbung wurde erstellt.',
    'enable_editing' => 'Bearbeitung aktivieren',
    'select_editing_mode' => 'Wählen Sie, wie die Bewerbung bearbeitet werden soll:',
    'edit_directly' => 'Direkt bearbeiten',
    'create_revision' => 'Neue Revision erstellen',
    'no_permission' => 'Sie haben keine Berechtigung für diese Aktion.',

    // Personal Data
    'personal_data' => 'Persönliche Daten',
    'age' => 'Alter',
    'years' => 'Jahre',
    'gender' => 'Geschlecht',
    'pronouns' => 'Pronomen',
    'discord_id' => 'Discord ID',

    // Abilities
    'professions_abilities' => 'Tätigkeiten & Fähigkeiten',
    'selected_professions' => 'Gewählte Tätigkeiten',
    'voice_type' => 'Stimmhöhe',
    'ram' => 'RAM',
    'fps' => 'FPS in Minecraft',
    'desired_role' => 'Wunschrolle',
    'languages' => 'Programmiersprachen',

    // Technical Details
    'technical_details' => 'Technische Details',
    'microphone' => 'Mikrofon',
    'daw' => 'DAW',
    'software' => 'Software',
    'design_style' => 'Design-Stil',
    'favorite_design' => 'Lieblings-Design',
    'gpu' => 'Grafikkarte (GPU)',
    'ide' => 'IDE',
    'confirmations' => 'Bestätigungen',

    // Confirmations
    'confirmation' => [
        'has_pc' => 'PC vorhanden',
        'has_java' => 'Besitzt Minecraft Java',
        'has_microphone' => 'Mikrofon vorhanden',
        'understands_roleplay' => 'Versteht, dass dies kein RP-Projekt ist',
        'agrees_to_rules' => 'Akzeptiert die Serverregeln',
    ],

    // About the Applicant
    'about_applicant' => 'Über den Bewerber',
    'about_me' => 'Über mich',
    'no_information' => 'Keine Angaben',
    'strengths_weaknesses' => 'Stärken und Schwächen',
    'final_words' => 'Abschlussworte',
    'portfolio' => 'Portfolio / Referenzen',

    // Internal Notes
    'internal_notes' => 'Interne Notizen',
    'edited_by' => 'Bearbeitet von',
    'on_date' => 'am',

    // Edit View
    'cancel' => 'Abbrechen',
    'update_status' => 'Status aktualisieren',
    'notes_about_applicant' => 'Interne Notizen zum Bewerber',
    'notes_description' => 'Diese Notizen sind nur für das Team sichtbar und können wichtige Informationen zum Bewerber enthalten.',
    'notes_placeholder' => 'Deine Notizen zur Bewerbung...',
    'save_changes' => 'Änderungen speichern',

    // Response Generator
    'back_to_details' => 'Zurück zur Detailansicht',
    'smart_response' => 'Smart-Antwort',
    'generate_response_title' => 'Antwort generieren',
    'copied_to_clipboard' => 'In die Zwischenablage kopiert!',
    'application_analysis' => 'Bewerbungsanalyse',
    'recommendation' => 'Empfehlung',
    'recommendation_acceptance' => 'Annahme',
    'recommendation_rejection' => 'Ablehnung',
    'quality_rating' => 'Qualitätsbewertung',
    'strengths' => 'Stärken',
    'weaknesses' => 'Schwächen',
    'no_strengths_detected' => 'Keine besonderen Stärken erkannt',
    'no_weaknesses_detected' => 'Keine besonderen Schwächen erkannt',
    'change_status' => 'Status ändern',
    'response_auto_generated' => 'Die Antwort wurde basierend auf der Analyse automatisch generiert. Sie können den Status bei Bedarf ändern.',
    'accepted' => 'Angenommen',
    'rejected' => 'Abgelehnt',
    'recommended' => 'Empfohlen',
    'rejection_reasons' => 'Ablehnungsgründe',
    'suggested_reasons_detected' => 'Basierend auf der Bewerbung wurden folgende Ablehnungsgründe erkannt:',
    'add_reason' => 'Hinzufügen',
    'rp_misunderstanding' => 'RP-Server Missverständnis',
    'no_pc' => 'Kein PC vorhanden',
    'no_pc_minecraft' => 'Kein PC & kein Minecraft Java',
    'insufficient_ram' => 'Zu wenig RAM',
    'insufficient_fps' => 'Zu wenig FPS',
    'no_microphone' => 'Kein Mikrofon',
    'no_daw' => 'Keine DAW',
    'too_young' => 'Zu jung (unter 14 Jahre)',
    'applicant_thinks_rp' => 'Bewerber denkt, wir sind ein RP-Server',
    'additional_reasons' => 'Weitere Gründe',
    'additional_reasons_placeholder' => 'Weitere Gründe für die Ablehnung eingeben...',
    'generated_response' => 'Generierte Antwort',
    'copy' => 'Kopieren',
    'preview' => 'Vorschau',
    'raw_text' => 'Rohtext',
    'no_preview_available' => 'Keine Vorschau verfügbar',
    'response_can_be_copied' => 'Die generierte Antwort kann in Discord als Nachricht kopiert werden. Klicke auf "Kopieren", um den Text in die Zwischenablage zu kopieren.',
    'cancel_button' => 'Abbrechen',
    'save_apply_status' => 'Status speichern & anwenden',

    // Profession names for filters (might overlap with application.php)
    'profession' => [
        'actor' => 'Schauspieler',
        'actor_no_voice' => 'Schauspieler (No Voice)',
        'voice_actor' => 'Synchronsprecher',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'cutter' => 'Cutter',
        'cameraman' => 'Kameramann',
        'developer' => 'Developer',
        'modeler' => '3D-Modellierer',
        'music_producer' => 'Musikproduzent',
    ],

    // Response Generator Messages
    'processing_application_data' => 'Bewerbungsdaten werden verarbeitet...',
    'error_processing_application' => 'Fehler beim Verarbeiten der Bewerbung',
    'try_again' => 'Erneut versuchen',
];
