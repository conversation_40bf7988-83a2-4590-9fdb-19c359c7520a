<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Models\Ticket; // For status constants if needed
use Exception;
use Discord\Builders\Components\ActionRow;
use Discord\Builders\Components\Button;

class DiscordTicketChannelService
{
    protected DiscordMessagingService $messagingService;
    protected string $botToken;
    protected const DISCORD_API_BASE_URL = 'https://discord.com/api/v10';

    // Permission bitwise values
    protected const PERM_SEND_MESSAGES = 1 << 11; // 2048
    protected const PERM_ATTACH_FILES = 1 << 15; // 32768
    protected const PERM_VIEW_CHANNEL = 1 << 10; // 1024
    protected const PERM_READ_MESSAGE_HISTORY = 1 << 16; // 65536
    protected const PERM_MANAGE_CHANNELS = 1 << 4; // 16
    protected const PERM_MANAGE_ROLES = 1 << 28; // 268435456


    public function __construct(DiscordMessagingService $messagingService)
    {
        $this->messagingService = $messagingService;
        $this->botToken = Config::get('minewache_discord_bot.token');
        if (empty($this->botToken)) {
            Log::error('DiscordTicketChannelService: Bot token is not configured.');
            // Consider throwing an exception here or handling this state appropriately
        }
    }

    /**
     * Creates a new ticket channel in Discord.
     *
     * @param string $guildId The Discord guild (server) ID
     * @param int $ticketId The ticket ID in the database
     * @param string $title The ticket title
     * @param string $discordUserId The Discord user ID of the ticket creator
     * @return array|null The channel data or null if creation failed
     */
    public function createTicketChannel(string $guildId, int $ticketId, string $title, string $discordUserId): ?array
    {
        if (empty($this->botToken)) {
            Log::error("DiscordTicketChannelService: Bot token not configured, cannot create channel for ticket #{$ticketId}.");
            return null;
        }

        try {
            // Sanitize the title for use in channel name
            $sanitizedTitle = substr(preg_replace('/[^a-z0-9_]/i', '', strtolower($title)), 0, 50);
            $channelName = "ticket-{$ticketId}-{$sanitizedTitle}";
            $channelName = substr($channelName, 0, 100); // Ensure name is within Discord's limits

            // Get the ticket category ID from config
            $ticketCategoryId = Config::get('minewache_discord_bot.ticket_category_id');

            // Create channel data
            $channelData = [
                'name' => $channelName,
                'type' => 0, // 0 for text channel
                'topic' => "Ticket ID: {$ticketId} - {$title}",
                'parent_id' => $ticketCategoryId ?? null,
                'permission_overwrites' => []
            ];

            // Add permission overwrites for @everyone (deny view)
            $channelData['permission_overwrites'][] = [
                'id' => $guildId, // @everyone role has same ID as guild
                'type' => 0, // 0 for role
                'allow' => '0',
                'deny' => (string) self::PERM_VIEW_CHANNEL
            ];

            // Add permission overwrites for ticket creator (allow view, send messages, attach files)
            $channelData['permission_overwrites'][] = [
                'id' => $discordUserId,
                'type' => 1, // 1 for member
                'allow' => (string) (self::PERM_VIEW_CHANNEL | self::PERM_SEND_MESSAGES | self::PERM_ATTACH_FILES | self::PERM_READ_MESSAGE_HISTORY),
                'deny' => '0'
            ];

            // Add permission overwrites for support roles if configured
            $supportRoleIds = Config::get('minewache_discord_bot.support_role_ids', []);
            foreach ($supportRoleIds as $roleId) {
                $channelData['permission_overwrites'][] = [
                    'id' => $roleId,
                    'type' => 0, // 0 for role
                    'allow' => (string) (self::PERM_VIEW_CHANNEL | self::PERM_SEND_MESSAGES | self::PERM_ATTACH_FILES | self::PERM_READ_MESSAGE_HISTORY | self::PERM_MANAGE_CHANNELS | self::PERM_MANAGE_ROLES),
                    'deny' => '0'
                ];
            }

            // Create the channel via Discord API
            $response = Http::withHeaders([
                'Authorization' => 'Bot ' . $this->botToken,
                'Content-Type' => 'application/json',
            ])->post(self::DISCORD_API_BASE_URL . "/guilds/{$guildId}/channels", $channelData);

            if ($response->successful()) {
                $channelInfo = $response->json();
                Log::info("DiscordTicketChannelService: Created channel {$channelInfo['id']} for ticket #{$ticketId}");

                // Send welcome message with ticket information
                $welcomeMessage = "# Ticket #{$ticketId}: {$title}\n\n";
                $welcomeMessage .= "Welcome <@{$discordUserId}>! Support staff will assist you shortly.\n\n";
                $welcomeMessage .= "Please provide any additional information that might help us resolve your issue.";

                $this->messagingService->sendMessageToChannel($channelInfo['id'], $welcomeMessage);

                // Create and send close ticket button
                $actionRow = new \Discord\Builders\Components\ActionRow();
                $actionRow->addComponent(
                    new \Discord\Builders\Components\Button(
                        \Discord\Builders\Components\Button::STYLE_DANGER,
                        "ticket_close_{$ticketId}",
                        "Close Ticket"
                    )
                );

                $buttonMessage = "Click the button below to close this ticket when your issue is resolved:";
                $this->messagingService->sendMessageToChannel(
                    $channelInfo['id'],
                    $buttonMessage,
                    [], // No embeds
                    [$actionRow]
                );

                return $channelInfo;
            } else {
                Log::error("DiscordTicketChannelService: Failed to create channel for ticket #{$ticketId}", [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }
        } catch (Exception $e) {
            Log::error("DiscordTicketChannelService: Exception while creating channel for ticket #{$ticketId}", [
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Processes the closure of a Discord ticket channel using REST API.
     * Sends a message, renames channel, updates permissions.
     *
     * @param string $channelId
     * @param Ticket $ticketModel The Laravel Ticket model.
     * @param string|null $reason
     * @param string|null $closedByTag User tag of who closed it.
     * @return bool
     */
    public function closeChannel(string $channelId, Ticket $ticketModel, ?string $reason, ?string $closedByTag = 'System'): bool
    {
        if (empty($this->botToken)) {
            Log::error("DiscordTicketChannelService: Bot token not configured, cannot close channel {$channelId}.");
            return false;
        }

        // 1. Send message (remains the same, uses DiscordMessagingService)
        $closureMessage = "Ticket #{$ticketModel->id} has been closed by {$closedByTag}.";
        if ($reason) $closureMessage .= "\nReason: " . htmlspecialchars($reason); // htmlspecialchars for safety if reason is user-provided
        $embedData = ['title' => 'Ticket Closed', 'description' => $closureMessage, 'color' => 0x28a745]; // Green color
        $embed = $this->messagingService->createEmbed($embedData);
        if ($embed) {
            $this->messagingService->sendMessageToChannel($channelId, '', [$embed]);
        } else {
            $this->messagingService->sendMessageToChannel($channelId, $closureMessage);
        }

        // 2. Rename channel via REST API
        $baseName = $ticketModel->title ? substr(preg_replace('/[^a-z0-9_]/i', '', strtolower($ticketModel->title)), 0, 50) : $ticketModel->id;
        $newChannelName = "closed-{$ticketModel->id}-{$baseName}";
        $newChannelName = substr($newChannelName, 0, 100); // Ensure name is within Discord's limits

        $renameResponse = Http::withHeaders([
            'Authorization' => 'Bot ' . $this->botToken,
            'Content-Type' => 'application/json',
        ])->patch(self::DISCORD_API_BASE_URL . "/channels/{$channelId}", [
            'name' => $newChannelName,
        ]);

        if ($renameResponse->successful()) {
            Log::info("DiscordTicketChannelService: Renamed channel {$channelId} to {$newChannelName} for ticket #{$ticketModel->id} via REST.");
        } else {
            Log::error("DiscordTicketChannelService: Error renaming channel {$channelId} for ticket #{$ticketModel->id} via REST.", [
                'status' => $renameResponse->status(),
                'response' => $renameResponse->body(),
            ]);
            // Optionally, decide if failure here should prevent further actions or return false
        }

        // 3. Update permissions for the ticket creator via REST API
        $ticketCreatorDiscordId = $ticketModel->discord_user_id;
        if ($ticketCreatorDiscordId) {
            // Fetch current permissions for the user to determine their existing 'allow' bitmask.
            // This is a simplification; real-world scenarios might need to fetch all overwrites
            // or have a known 'base' permission set. For this refactor, we assume we need to modify
            // existing permissions based on a typical ticket setup.
            // A GET request to /channels/{channel.id} would give all overwrites.
            // For simplicity, we'll construct a new permission set that removes write access.
            // We assume the user initially had VIEW_CHANNEL, SEND_MESSAGES, ATTACH_FILES.
            // New permissions: VIEW_CHANNEL (allow), SEND_MESSAGES (deny), ATTACH_FILES (deny).

            // Let's assume the user initially had: VIEW_CHANNEL | SEND_MESSAGES | ATTACH_FILES
            // We want to keep VIEW_CHANNEL, but remove SEND_MESSAGES and ATTACH_FILES.
            // So, the new 'allow' will be just VIEW_CHANNEL.
            // The new 'deny' will explicitly include SEND_MESSAGES and ATTACH_FILES.

            // It's often better to fetch the current permissions to correctly calculate the new bitmask.
            // However, the original code modified existing permissions. Let's try to replicate that behavior.
            // The original code did:
            // $newAllowPermissions->remove(ChannelPermission::SEND_MESSAGES());
            // $newAllowPermissions->remove(ChannelPermission::ATTACH_FILES());
            // This implies we need to know the original 'allow' value.
            // Without fetching the channel details, we can't know the original 'allow' value.
            // For this refactor, let's set a defined state: allow VIEW_CHANNEL, deny SEND_MESSAGES & ATTACH_FILES.
            // This is a common state for a closed ticket channel for the original user.

            $newAllowBitwise = self::PERM_VIEW_CHANNEL;
            $newDenyBitwise = self::PERM_SEND_MESSAGES | self::PERM_ATTACH_FILES;

            $permissionsResponse = Http::withHeaders([
                'Authorization' => 'Bot ' . $this->botToken,
                'Content-Type' => 'application/json',
            ])->put(self::DISCORD_API_BASE_URL . "/channels/{$channelId}/permissions/{$ticketCreatorDiscordId}", [
                'id' => $ticketCreatorDiscordId,
                'type' => 1, // 1 for a member overwrite
                'allow' => (string) $newAllowBitwise,
                'deny' => (string) $newDenyBitwise,
            ]);

            if ($permissionsResponse->successful()) {
                Log::info("DiscordTicketChannelService: Updated permissions for creator {$ticketCreatorDiscordId} in channel {$channelId} via REST.");
            } else {
                Log::error("DiscordTicketChannelService: Error updating permissions for creator {$ticketCreatorDiscordId} in {$channelId} via REST.", [
                    'status' => $permissionsResponse->status(),
                    'response' => $permissionsResponse->body(),
                ]);
            }
        } else {
            Log::warning("DiscordTicketChannelService: No ticket creator Discord ID found for ticket #{$ticketModel->id}, skipping permission update.");
        }

        return true; // Assuming success unless a critical error causes an early return
    }
}
