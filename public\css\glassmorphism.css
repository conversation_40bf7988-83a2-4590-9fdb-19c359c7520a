/* Glassmorphism effect styles */
.glassmorphism {
    position: relative;
    /* background-color: rgba(255, 255, 255, 0.4); */
    /* backdrop-filter: blur(12px); */
    /* -webkit-backdrop-filter: blur(12px); */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Dark mode adjustments for glassmorphism */
@media (prefers-color-scheme: dark) {
    .glassmorphism {
        /* background-color: rgba(30, 30, 30, 0.3); */
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
}

/* Improved glassmorphism for better contrast */
.glassmorphism h1,
.glassmorphism h2,
.glassmorphism h3,
.glassmorphism h4,
.glassmorphism h5,
.glassmorphism h6 {
    color: rgba(0, 0, 0, 0.95);
}

.glassmorphism p {
    color: rgba(0, 0, 0, 0.85);
}

/* Dark mode text adjustments */
@media (prefers-color-scheme: dark) {
    .glassmorphism h1,
    .glassmorphism h2,
    .glassmorphism h3,
    .glassmorphism h4,
    .glassmorphism h5,
    .glassmorphism h6 {
        color: rgba(255, 255, 255, 0.95);
    }

    .glassmorphism p {
        color: rgba(255, 255, 255, 0.85);
    }
}

/* Add subtle inner border for better definition */
.glassmorphism::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), transparent);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    pointer-events: none;
}

/* Dark mode inner border adjustment */
@media (prefers-color-scheme: dark) {
    .glassmorphism::before {
        background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.1), transparent);
    }
}

/* Ensure proper text contrast for fractional opacity classes */
.text-base-content\/80 {
    color: rgba(0, 0, 0, 0.85);
}

.text-base-content\/70 {
    color: rgba(0, 0, 0, 0.75);
}

@media (prefers-color-scheme: dark) {
    .text-base-content\/80 {
        color: rgba(255, 255, 255, 0.85);
    }

    .text-base-content\/70 {
        color: rgba(255, 255, 255, 0.75);
    }
}

#glCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1; /* Ensure it's behind other content */
}
