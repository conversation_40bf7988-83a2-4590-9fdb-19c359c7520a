<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class ApplicationController extends Controller
{
    /**
     * Zeigt eine Übersicht aller Bewerbungen an (für Admin/Team-Mitglieder)
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function index()
    {
        $applications = Application::orderBy('created_at', 'desc')->get();

        // Animation-Meta-Daten für Frontend
        $animationMeta = [
            'entryAnimation' => 'fade-in',
            'listItemAnimation' => 'slide-up',
            'listItemDelay' => 0.05, // Sekunden zwischen den Animationen für Cascade-Effekt
        ];

        return view('applications.index', compact('applications', 'animationMeta'));
    }

    /**
     * <PERSON><PERSON>gt eine einzelne Bewerbung an
     *
     * @param string $discord_id Discord-ID des Bewerbers
     * @param int $id ID der Bewerbung
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function show($discord_id, $id)
    {
        try {
            $application = Application::where('discord_id', $discord_id)
                ->where('id', $id)
                ->firstOrFail();

            $user = User::find($discord_id);

            // Animation-Meta-Daten für Frontend
            $animationMeta = [
                'entryAnimation' => 'fade-in',
                'sectionsAnimation' => 'slide-up',
                'sectionsDelay' => 0.1, // Sekunden zwischen den Abschnitts-Animationen
            ];

            return view('applications.show', compact('application', 'user', 'animationMeta'));
        } catch (\Exception $e) {
            Log::error('Fehler beim Anzeigen der Bewerbung', [
                'discord_id' => $discord_id,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('applications.index')
                ->with('error', 'Die angeforderte Bewerbung konnte nicht gefunden werden.');
        }
    }

    /**
     * Speichert eine neue Bewerbung in der Datenbank
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            // Sensible Daten nicht vollständig loggen
            \Log::info('Bewerbung wird gespeichert', [
                'user_id' => Auth::id(),
                'discord_id' => $request->input('discord_id'),
                'name' => $request->input('name'),
                'professions' => $request->input('professions'),
            ]);

            $validatedData = $request->validate([
                'discord_id' => 'required|integer',
                'user_id' => 'nullable|string',
                'name' => 'required|string|max:255',
                'age' => 'required|integer|min:14|max:120',
                'gender' => 'required|string|max:50',
                'pronouns' => 'nullable|string|max:50',
                'professions' => 'required|array|min:1',
                'professions.*' => 'string|max:50|in:actor,builder,designer,developer,other',
                'otherProfession' => 'nullable|required_if:professions.*,other|string|max:255',
                'checkboxQuestions' => 'nullable|array',
                'checkboxQuestions.*' => 'boolean',
                'about_you' => 'required|string|min:50|max:5000',
                'strengths_weaknesses' => 'required|string|min:50|max:5000',
                'final_words' => 'nullable|string|max:1000',
                'voice_type' => 'nullable|string|max:255',
                'ram' => 'nullable|integer|min:0|max:256',
                'fps' => 'nullable|string|max:50',
                'desired_role' => 'nullable|string|max:255',
                'portfolio' => 'nullable|string|url|max:2000',
                'microphone' => 'nullable|string|max:255',
                'daw' => 'nullable|string|max:255',
                'program' => 'nullable|string|max:255',
                'design_style' => 'nullable|string|max:255',
                'favorite_design' => 'nullable|string|max:255',
                'gpu' => 'nullable|string|max:255',
                'languages' => 'nullable|string|max:255',
                'ide' => 'nullable|string|max:255',
            ], [
                'name.required' => 'Bitte gib deinen Namen an.',
                'age.required' => 'Bitte gib dein Alter an.',
                'age.min' => 'Du musst mindestens 14 Jahre alt sein.',
                'age.max' => 'Das angegebene Alter ist zu hoch.',
                'gender.required' => 'Bitte wähle ein Geschlecht aus.',
                'professions.required' => 'Bitte wähle mindestens eine Tätigkeit aus.',
                'professions.min' => 'Bitte wähle mindestens eine Tätigkeit aus.',
                'otherProfession.required_if' => 'Bitte gib deine andere Tätigkeit an.',
                'about_you.required' => 'Bitte erzähle uns etwas über dich.',
                'about_you.min' => 'Der Text über dich sollte mindestens 50 Zeichen enthalten.',
                'strengths_weaknesses.required' => 'Bitte beschreibe deine Stärken und Schwächen.',
                'strengths_weaknesses.min' => 'Der Text über deine Stärken und Schwächen sollte mindestens 50 Zeichen enthalten.',
            ]);

            $application = new Application();
            // Verknüpfung mit aktuell eingeloggtem User
            $application->user_id = Auth::id();
            // Fallback: Wenn user_id aus dem Formular kommt, verwenden wir diesen
            if (isset($validatedData['user_id'])) {
                $application->user_id = $validatedData['user_id'];
            }
            $application->discord_id = $validatedData['discord_id'];
            $application->name = $validatedData['name'];
            $application->age = $validatedData['age'];
            $application->gender = $validatedData['gender'];
            $application->pronouns = $validatedData['pronouns'];
            $application->professions = $validatedData['professions'];

            // Wenn "other" ausgewählt wurde, fügen wir die benutzerdefinierte Tätigkeit hinzu
            if(in_array('other', $validatedData['professions']) && isset($validatedData['otherProfession'])) {
                $application->other_profession = $validatedData['otherProfession'];
            }

            $application->checkboxQuestions = $validatedData['checkboxQuestions'] ?? [];
            $application->about_you = $validatedData['about_you'];
            $application->strengths_weaknesses = $validatedData['strengths_weaknesses'];
            $application->final_words = $validatedData['final_words'] ?? null;
            $application->voice_type = $validatedData['voice_type'] ?? null;
            $application->ram = $validatedData['ram'] ?? null;
            $application->fps = $validatedData['fps'] ?? null;
            $application->desired_role = $validatedData['desired_role'] ?? null;
            $application->portfolio = $validatedData['portfolio'] ?? null;
            $application->microphone = $validatedData['microphone'] ?? null;
            $application->daw = $validatedData['daw'] ?? null;
            $application->program = $validatedData['program'] ?? null;
            $application->design_style = $validatedData['design_style'] ?? null;
            $application->favorite_design = $validatedData['favorite_design'] ?? null;
            $application->gpu = $validatedData['gpu'] ?? null;
            $application->languages = $validatedData['languages'] ?? null;
            $application->ide = $validatedData['ide'] ?? null;
            $application->status = 'pending'; // Standardstatus
            $application->editable = true; // Standardmäßig bearbeitbar
            $application->created_at = now();
            $application->save();

            // Erfolgreiche Rückmeldung mit Animation-Hint
            return redirect()->route('my.applications')
                ->with('success', 'Bewerbung erfolgreich eingereicht! Wir werden sie so schnell wie möglich prüfen.')
                ->with('animation', 'slide-up'); // Animation-Hint für Frontend

        } catch (ValidationException $e) {
            // Nur die Validierungsfehler loggen, nicht die Eingabedaten
            \Log::error('Validierungsfehler bei Bewerbung', [
                'user_id' => Auth::id(),
                'errors' => array_keys($e->errors())
            ]);

            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error-animation', 'shake'); // Animation-Hint für Fehler
        } catch (\Exception $e) {
            // Fehler loggen ohne sensible Benutzerdaten
            \Log::error('Fehler beim Speichern der Bewerbung', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return redirect()->back()
                ->with('error', 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuche es später erneut.')
                ->withInput()
                ->with('error-animation', 'shake');
        }
    }

    /**
     * Zeigt alle Bewerbungen des aktuell eingeloggten Benutzers an
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function myApplications()
    {
        // Suche nach Bewerbungen, die entweder mit user_id oder discord_id verknüpft sind
        $applications = Application::where(function($query) {
                $query->where('user_id', Auth::id())
                      ->orWhere('discord_id', Auth::id());
            })
            ->orderByDesc('created_at')
            ->get();

        // Debug-Informationen
        \Log::info('Meine Bewerbungen für Benutzer: ' . Auth::id(), [
            'user_id' => Auth::id(),
            'applications_count' => $applications->count(),
            'applications' => $applications->pluck('id')->toArray()
        ]);

        // Animation-Meta-Daten für Frontend
        $animationMeta = [
            'entryAnimation' => 'fade-in',
            'listItemAnimation' => 'slide-up',
            'listItemDelay' => 0.1, // Sekunden zwischen den Animationen für Cascade-Effekt
        ];

        return view('applications.my-applications', compact('applications', 'animationMeta'));
    }

    /**
     * Zeigt die Details einer einzelnen Bewerbung des Benutzers an
     *
     * @param int $id ID der Bewerbung
     * @return \Illuminate\Http\RedirectResponse
     */
    public function viewMyApplication($id)
    {
        try {
            $application = $this->getUserApplication($id);

            // Animation-Meta-Daten für Frontend
            $animationMeta = [
                'entryAnimation' => 'fade-in',
                'sectionAnimation' => 'slide-up',
                'sectionDelay' => 0.1, // Sekunden zwischen den Abschnitts-Animationen
            ];

            // Status-spezifische Klassen für visuelle Darstellung
            $statusClasses = $this->getStatusVisualClasses($application->status);

            return view('applications.my-application-show', compact(
                'application',
                'animationMeta',
                'statusClasses'
            ));

        } catch (\Exception $e) {
            Log::error('Fehler beim Anzeigen der eigenen Bewerbung', [
                'id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('my.applications')
                ->with('error', 'Die angeforderte Bewerbung konnte nicht gefunden werden.');
        }
    }

    /**
     * Zeigt das Bearbeitungsformular für eine Bewerbung,
     * aber nur wenn diese zur Bearbeitung freigegeben ist
     *
     * @param int $id ID der Bewerbung
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function editMyApplication($id)
    {
        try {
            $application = $this->getUserApplication($id);

            // Prüfe, ob die Bewerbung bearbeitet werden darf
            if (!$application->editable) {
                return redirect()->route('my.applications.show', $application->id)
                    ->with('error', __('application.application_not_editable'))
                    ->with('animation', 'shake');
            }

            // Verwende den ApplicationWizard für die Bearbeitung
            return view('application_edit', ['applicationId' => $application->id]);

        } catch (\Exception $e) {
            Log::error('Fehler beim Bearbeiten der eigenen Bewerbung', [
                'id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('my.applications')
                ->with('error', 'Die angeforderte Bewerbung konnte nicht gefunden werden.')
                ->with('animation', 'shake');
        }
    }

    /**
     * Aktualisiert eine existierende Bewerbung
     *
     * @param Request $request
     * @param int $id ID der Bewerbung
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateMyApplication(Request $request, $id)
    {
        try {
            $application = $this->getUserApplication($id);

            // Prüfen, ob die Bewerbung bearbeitet werden darf
            if (!$application->editable) {
                return redirect()->route('my.applications')
                    ->with('error', 'Diese Bewerbung kann nicht bearbeitet werden. Ein Administrator muss die Bearbeitung freischalten.');
            }

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'age' => 'required|integer|min:14|max:120',
                'gender' => 'required|string',
                'pronouns' => 'nullable|string|max:50',
                'professions' => 'required|array|min:1',
                'otherProfession' => 'nullable|required_if:professions.*,other|string|max:255',
                'checkboxQuestions' => 'nullable|array',
                'about_you' => 'required|string|min:50',
                'strengths_weaknesses' => 'required|string|min:50',
                'final_words' => 'nullable|string',
                'voice_type' => 'nullable|string|max:255',
                'ram' => 'nullable|integer|min:0|max:256',
                'fps' => 'nullable|string|max:50',
                'desired_role' => 'nullable|string|max:255',
                'portfolio' => 'nullable|string|url|max:2000',
                'microphone' => 'nullable|string|max:255',
                'daw' => 'nullable|string|max:255',
                'program' => 'nullable|string|max:255',
                'design_style' => 'nullable|string|max:255',
                'favorite_design' => 'nullable|string|max:255',
                'gpu' => 'nullable|string|max:255',
                'languages' => 'nullable|string|max:255',
                'ide' => 'nullable|string|max:255',
            ], [
                'name.required' => 'Bitte gib deinen Namen an.',
                'age.required' => 'Bitte gib dein Alter an.',
                'age.min' => 'Du musst mindestens 14 Jahre alt sein.',
                'age.max' => 'Das angegebene Alter ist zu hoch.',
                'gender.required' => 'Bitte wähle ein Geschlecht aus.',
                'professions.required' => 'Bitte wähle mindestens eine Tätigkeit aus.',
                'professions.min' => 'Bitte wähle mindestens eine Tätigkeit aus.',
                'otherProfession.required_if' => 'Bitte gib deine andere Tätigkeit an.',
                'about_you.required' => 'Bitte erzähle uns etwas über dich.',
                'about_you.min' => 'Der Text über dich sollte mindestens 50 Zeichen enthalten.',
                'strengths_weaknesses.required' => 'Bitte beschreibe deine Stärken und Schwächen.',
                'strengths_weaknesses.min' => 'Der Text über deine Stärken und Schwächen sollte mindestens 50 Zeichen enthalten.',
            ]);

            // Status zurücksetzen, wenn Bewerbung bearbeitet wird
            if ($application->status !== 'pending') {
                $application->status = 'pending';
            }

            // Bearbeitbarkeit beibehalten, damit der Admin entscheiden kann, ob weitere Änderungen erlaubt sind

            // Speichere die Änderungen
            $application->name = $validatedData['name'];
            $application->age = $validatedData['age'];
            $application->gender = $validatedData['gender'];
            $application->pronouns = $validatedData['pronouns'];
            // Professions werden nicht aktualisiert, um zu verhindern, dass Benutzer Rollen hinzufügen
            // Stattdessen behalten wir die ursprünglichen Professions bei
            // $application->professions = $validatedData['professions'];

            // Wenn "other" in den ursprünglichen Professions war, behalten wir die benutzerdefinierte Tätigkeit bei
            if(in_array('other', $application->professions) && isset($validatedData['otherProfession'])) {
                $application->other_profession = $validatedData['otherProfession'];
            }

            $application->checkboxQuestions = $validatedData['checkboxQuestions'] ?? [];
            $application->about_you = $validatedData['about_you'];
            $application->strengths_weaknesses = $validatedData['strengths_weaknesses'];
            $application->final_words = $validatedData['final_words'] ?? null;
            $application->voice_type = $validatedData['voice_type'] ?? null;
            $application->ram = $validatedData['ram'] ?? null;
            $application->fps = $validatedData['fps'] ?? null;
            $application->desired_role = $validatedData['desired_role'] ?? null;
            $application->portfolio = $validatedData['portfolio'] ?? null;
            $application->microphone = $validatedData['microphone'] ?? null;
            $application->daw = $validatedData['daw'] ?? null;
            $application->program = $validatedData['program'] ?? null;
            $application->design_style = $validatedData['design_style'] ?? null;
            $application->favorite_design = $validatedData['favorite_design'] ?? null;
            $application->gpu = $validatedData['gpu'] ?? null;
            $application->languages = $validatedData['languages'] ?? null;
            $application->ide = $validatedData['ide'] ?? null;
            $application->updated_at = now();

            $application->save();

            return redirect()->route('my.applications')
                ->with('success', 'Deine Bewerbung wurde erfolgreich aktualisiert und wird erneut geprüft.')
                ->with('animation', 'slide-up');

        } catch (ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error-animation', 'shake');

        } catch (\Exception $e) {
            Log::error('Fehler beim Aktualisieren der Bewerbung', [
                'id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuche es später erneut.')
                ->withInput()
                ->with('error-animation', 'shake');
        }
    }

    /**
     * Zeigt das Bewerbungsformular an
     *
     * @return \Illuminate\View\View
     */
    public function showApplicationForm()
    {
        $animationMeta = [
            'formEntryAnimation' => 'fade-in',
            'formStepsAnimation' => 'slide-up',
            'formFieldsAnimation' => 'slide-right',
            'formFieldsDelay' => 0.05, // Sekunden zwischen Feld-Animationen
        ];

        return view('bewerben', compact('animationMeta')); // Using existing view file
    }

    /**
     * Holt eine Bewerbung des aktuellen Benutzers oder gibt eine 404-Fehler zurück.
     *
     * @param int $id ID der Bewerbung
     * @return \App\Models\Application
     */
    protected function getUserApplication($id)
    {
        // Explizit firstOrFail verwenden, um 404 zu werfen, wenn die Bewerbung nicht existiert
        // oder nicht dem aktuellen Benutzer gehört
        $application = Application::where('id', $id)
            ->where(function($query) {
                $query->where('user_id', Auth::id())
                      ->orWhere('discord_id', Auth::id());
            })
            ->firstOrFail();

        // Wenn die Bewerbung gefunden wurde, aber user_id nicht gesetzt ist,
        // aktualisieren wir sie mit der aktuellen Benutzer-ID
        if ($application && !$application->user_id) {
            $application->user_id = Auth::id();
            $application->save();
        }

        return $application;
    }

    /**
     * Schaltet die Bearbeitbarkeit einer Bewerbung um (nur für Admins)
     *
     * @param int $id ID der Bewerbung
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function toggleEditability($id)
    {
        try {
            $application = Application::findOrFail($id);
            $application->editable = !$application->editable;
            $application->save();

            $status = $application->editable ? 'freigegeben' : 'gesperrt';

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Die Bearbeitung der Bewerbung wurde {$status}.",
                    'editable' => $application->editable
                ]);
            }

            return redirect()->back()->with('message', "Die Bearbeitung der Bewerbung wurde {$status}.");
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Fehler beim Ändern der Bearbeitbarkeit: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Fehler beim Ändern der Bearbeitbarkeit: ' . $e->getMessage());
        }
    }

    /**
     * Zeigt das Bearbeitungsformular für eine Bewerbung (Admin-Version)
     *
     * @param int $id ID der Bewerbung
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function editApplication($id)
    {
        try {
            $application = Application::findOrFail($id);

            // Prüfe, ob der Benutzer berechtigt ist, die Bewerbung zu bearbeiten
            if (!Gate::allows('MINEWACHE_TEAM')) {
                return redirect()->route('admin.applications')
                    ->with('error', __('admin.no_permission'))
                    ->with('animation', 'shake');
            }

            // Verwende den ApplicationWizard für die Bearbeitung
            return view('application_edit', ['applicationId' => $application->id]);

        } catch (\Exception $e) {
            return redirect()->route('admin.applications')
                ->with('error', 'Fehler beim Laden der Bewerbung: ' . $e->getMessage())
                ->with('animation', 'shake');
        }
    }

    /**
     * Liefert CSS-Klassen für visuelle Darstellung des Status
     *
     * @param string $status Status der Bewerbung
     * @return array Array mit CSS-Klassen für verschiedene Elemente
     */
    protected function getStatusVisualClasses($status)
    {
        switch ($status) {
            case 'approved':
                return [
                    'badge' => 'badge-success',
                    'icon' => 'check-circle',
                    'bg' => 'bg-success/10',
                    'border' => 'border-success/30',
                    'text' => 'text-success',
                    'animation' => 'pulse',
                ];
            case 'rejected':
                return [
                    'badge' => 'badge-error',
                    'icon' => 'x-circle',
                    'bg' => 'bg-error/10',
                    'border' => 'border-error/30',
                    'text' => 'text-error',
                    'animation' => '',
                ];
            default: // pending
                return [
                    'badge' => 'badge-warning',
                    'icon' => 'clock',
                    'bg' => 'bg-warning/10',
                    'border' => 'border-warning/30',
                    'text' => 'text-warning',
                    'animation' => 'bounce',
                ];
        }
    }
}
