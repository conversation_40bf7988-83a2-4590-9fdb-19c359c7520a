<?php

namespace App\Providers;

use App\Broadcasting\CustomBroadcastEvent;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Broadcasting\BroadcastEvent;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\Factory as BroadcastingFactory;

class CustomBroadcastingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Override the BroadcastEvent job to always use Reverb
        $this->app->bind(BroadcastEvent::class, function ($app, $parameters) {
            $event = $parameters['event'] ?? null;

            if ($event instanceof ShouldBroadcast) {
                Log::info('CustomBroadcastingServiceProvider: Creating CustomBroadcastEvent for ' . get_class($event));
                return new CustomBroadcastEvent($event);
            }

            return new BroadcastEvent($event);
        });

        // Override the event dispatcher to use our custom broadcast event
        $this->app->extend('events', function ($dispatcher, $app) {
            $dispatcher->listen(ShouldBroadcast::class, function ($event) {
                Log::info('Intercepted ShouldBroadcast event', ['event' => get_class($event)]);

                // Use our custom broadcast event class
                Queue::connection(config('queue.default'))->push(
                    new CustomBroadcastEvent($event)
                );
            });

            return $dispatcher;
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Log when the service provider is booted, conditionally
        if (config('app.debug', false)) {
            Log::info('CustomBroadcastingServiceProvider booted');
        }
    }
}
