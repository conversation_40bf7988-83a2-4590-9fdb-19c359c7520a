<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Application;
use App\Models\User;
use App\Enums\Role;
use App\Services\ResponseGeneratorService;
use App\Services\DiscordService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ResponseGenerator extends Component
{
    // Eigenschaften für die Bewerbung und die generierte Antwort
    public $applicationId = null;
    public $application = null;
    public $applicationStatus = 'pending';  // Standardwert setzen
    public $generatedResponse = [
        'markdown' => '',
        'copyable' => '',
        'discord' => '',
        'suggested_reasons' => []
    ];  // Als Array initialisieren
    public $customReasons = '';
    public $selectedReasons = [];
    public $suggestedReasons = [];
    public $applicationQuality = 0;
    public $applicationStrengths = [];
    public $applicationWeaknesses = [];
    public $processingState = '';  // Status für UI-Feedback
    public $discordBotStatus = false; // Status des Discord-Bots
    public $showSmartAnalysis = true; // Smart-Analyse anzeigen
    public $sendToDiscord = false; // Nachricht an Discord senden
    public $discordMessageSent = false; // Wurde die Nachricht gesendet?
    public $discordMessageStatus = null; // Status der Discord-Nachricht

    // Services für die Antwortgenerierung und Discord-Kommunikation
    protected $responseGenerator;
    protected $discordService;

    // Event-Listener
    protected $listeners = [
        'showResponseGenerator' => 'loadApplication',
        'refreshResponseGenerator' => 'refreshData',
        'generateDiscordPreview' => 'generateDiscordPreview'
    ];

    // Dispatch-Listener
    protected function getListeners()
    {
        return [
            'showResponseGenerator' => 'loadApplication',
            'refreshResponseGenerator' => 'refreshData',
            'generateDiscordPreview' => 'generateDiscordPreview'
        ];
    }

    /**
     * Mount the component and load application data if ID is provided
     */
    public function mount($applicationId = null)
    {
        if ($applicationId) {
            $this->loadApplication($applicationId);
        }
    }

    /**
     * Constructor for the component
     */
    public function boot(ResponseGeneratorService $responseGenerator, DiscordService $discordService)
    {
        $this->responseGenerator = $responseGenerator;
        $this->discordService = $discordService;

        // Prüfe Discord-Bot-Status
        $this->checkDiscordBotStatus();
    }

    /**
     * Load application data when component is mounted or when an application is selected
     */
    public function loadApplication($id)
    {
        try {
            $this->processingState = 'loading';
            $this->dispatch('processing-started');

            Log::info('ResponseGenerator: Lade Bewerbung', ['application_id' => $id]);

            $this->applicationId = $id;
            $this->application = Application::find($id);

            if (!$this->application) {
                Log::warning('ResponseGenerator: Bewerbung nicht gefunden', ['application_id' => $id]);
                session()->flash('error', 'Bewerbung nicht gefunden.');
                $this->dispatch('backToList');
                $this->processingState = '';
                return;
            }

            // Reset values
            $this->reset(['selectedReasons', 'suggestedReasons', 'customReasons',
                          'applicationQuality', 'applicationStrengths', 'applicationWeaknesses']);

            // Analyze the application
            Log::info('ResponseGenerator: Bewerbung wird analysiert', ['application_id' => $id]);
            $analysis = $this->responseGenerator->analyzeApplication($this->application);

            // Validiere das Analyse-Ergebnis
            if (!isset($analysis['recommended_status']) || !isset($analysis['rejection_reasons'])) {
                Log::error('ResponseGenerator: Unvollständiges Analyse-Ergebnis', [
                    'application_id' => $id,
                    'analysis' => $analysis
                ]);
                throw new \Exception('Die Analyse der Bewerbung ergab ein unvollständiges Ergebnis.');
            }

            // Set status and reasons based on the analysis
            $this->applicationStatus = $analysis['recommended_status'];
            $this->selectedReasons = $analysis['rejection_reasons'];

            // Generate response
            Log::info('ResponseGenerator: Antwort wird generiert', ['application_id' => $id]);
            $this->generatedResponse = $this->responseGenerator->generateSmartResponse($this->application);

            // Validiere die generierte Antwort
            if (!isset($this->generatedResponse['markdown']) || !isset($this->generatedResponse['copyable'])) {
                Log::error('ResponseGenerator: Unvollständige generierte Antwort', [
                    'application_id' => $id,
                    'response_keys' => array_keys($this->generatedResponse)
                ]);
                throw new \Exception('Die generierte Antwort ist unvollständig.');
            }

            // Save application quality and strengths/weaknesses
            $this->applicationQuality = $analysis['quality_score'] ?? 0;
            $this->applicationStrengths = $analysis['strengths'] ?? [];
            $this->applicationWeaknesses = $analysis['weaknesses'] ?? [];

            // Show notification
            $statusText = $this->applicationStatus === 'approved' ? 'Annahme' : 'Ablehnung';
            session()->flash('message', "Basierend auf der Analyse wird eine {$statusText} empfohlen. Qualitätsbewertung: {$this->applicationQuality}/100");

            // Signal erfolgreiche Verarbeitung
            $this->dispatch('response-generated', status: $this->applicationStatus);
            $this->processingState = 'success';
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Laden der Bewerbung', [
                'application_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Fehler beim Laden der Bewerbung: ' . $e->getMessage());
            $this->processingState = 'error';

            // Grundlegende Daten zurücksetzen
            $this->reset(['generatedResponse']);
            $this->generatedResponse = [
                'markdown' => 'Fehler bei der Antwortgenerierung. Bitte versuchen Sie es erneut.',
                'copyable' => '',
                'discord' => '',
                'suggested_reasons' => []
            ];
        }
    }

    /**
     * Aktualisiert die Daten der Komponente
     */
    public function refreshData()
    {
        if ($this->applicationId) {
            $this->loadApplication($this->applicationId);
        }
    }

    /**
     * Go back to application detail
     */
    public function backToDetail()
    {
        try {
            Log::info('ResponseGenerator: Zurück zur Detailansicht', [
                'application_id' => $this->applicationId
            ]);

            if ($this->application) {
                $this->dispatch('showApplicationDetail', id: $this->application->id);
            } else {
                $this->dispatch('backToList');
            }
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Zurückgehen zur Detailansicht', [
                'error' => $e->getMessage()
            ]);

            // Im Fehlerfall zur Listansicht zurückkehren
            $this->dispatch('backToList');
        }
    }

    /**
     * Generate a response based on the selected parameters
     */
    public function generateResponse()
    {
        try {
            $this->processingState = 'processing';
            $this->dispatch('processing-started');

            if (!$this->application) {
                session()->flash('error', 'Keine Bewerbung ausgewählt.');
                $this->processingState = 'error';
                return;
            }

            Log::info('ResponseGenerator: Antwort wird generiert', [
                'application_id' => $this->applicationId,
                'status' => $this->applicationStatus,
                'reasons_count' => count($this->selectedReasons)
            ]);

            // If the status has changed, we generate a new response
            // but keep the analysis results
            $this->generatedResponse = $this->responseGenerator->generateFullResponse(
                $this->application,
                $this->applicationStatus,
                $this->selectedReasons,
                $this->customReasons,
                true // Smart mode is always active
            );

            // Validiere die generierte Antwort
            if (!isset($this->generatedResponse['markdown']) || !isset($this->generatedResponse['copyable'])) {
                Log::warning('ResponseGenerator: Unvollständige generierte Antwort', [
                    'application_id' => $this->applicationId,
                    'response_keys' => array_keys($this->generatedResponse)
                ]);

                // Stelle sicher, dass zumindest leere Werte vorhanden sind
                $this->generatedResponse['markdown'] = $this->generatedResponse['markdown'] ?? '';
                $this->generatedResponse['copyable'] = $this->generatedResponse['copyable'] ?? '';
                $this->generatedResponse['discord'] = $this->generatedResponse['discord'] ?? '';
                $this->generatedResponse['suggested_reasons'] = $this->generatedResponse['suggested_reasons'] ?? [];
            }

            // Generiere Discord-Antwort, wenn Discord-Bot online ist
            if ($this->discordBotStatus) {
                $this->generateDiscordPreview();
            }

            // If no manual reasons were selected, show the suggested reasons
            if (empty($this->selectedReasons) && isset($this->generatedResponse['suggested_reasons'])) {
                $this->suggestedReasons = $this->generatedResponse['suggested_reasons'];
            }

            // Signal erfolgreiche Verarbeitung
            $this->processingState = 'success';

            // Stelle sicher, dass alle erforderlichen Schlüssel vorhanden sind
            if (!isset($this->generatedResponse['markdown'])) {
                $this->generatedResponse['markdown'] = '';
            }
            if (!isset($this->generatedResponse['copyable'])) {
                $this->generatedResponse['copyable'] = '';
            }
            if (!isset($this->generatedResponse['discord'])) {
                $this->generatedResponse['discord'] = '';
            }
            if (!isset($this->generatedResponse['suggested_reasons'])) {
                $this->generatedResponse['suggested_reasons'] = [];
            }

            // Log für Debugging
            Log::debug('ResponseGenerator: Antwort generiert', [
                'markdown_length' => strlen($this->generatedResponse['markdown']),
                'copyable_length' => strlen($this->generatedResponse['copyable']),
                'discord_length' => isset($this->generatedResponse['discord']) ? strlen($this->generatedResponse['discord']) : 0,
                'processing_state' => $this->processingState
            ]);

            // Dispatch Events, um die Antwort anzuzeigen und die Tabs zu initialisieren
            $this->dispatch('response-generated');
            $this->dispatch('response-updated');

            // Verzögerung hinzufügen, um sicherzustellen, dass die Komponente vollständig gerendert ist
            usleep(100000); // 100ms Verzögerung
            $this->dispatch('response-component-rendered');

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler bei der Antwortgenerierung', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Fehler bei der Antwortgenerierung: ' . $e->getMessage());
            $this->processingState = 'error';
        }
    }

    /**
     * Generate a smart response based on the application data
     */
    public function generateSmartResponse()
    {
        try {
            $this->processingState = 'processing';
            $this->dispatch('processing-started');

            if (!$this->application) {
                session()->flash('error', 'Keine Bewerbung ausgewählt.');
                $this->processingState = 'error';
                return;
            }

            Log::info('ResponseGenerator: Smart-Antwort wird generiert', [
                'application_id' => $this->applicationId
            ]);

            // Analyze the application
            $analysis = $this->responseGenerator->analyzeApplication($this->application);

            // Validiere das Analyse-Ergebnis
            if (!isset($analysis['recommended_status']) || !isset($analysis['rejection_reasons'])) {
                Log::warning('ResponseGenerator: Unvollständiges Analyse-Ergebnis', [
                    'application_id' => $this->applicationId,
                    'analysis_keys' => array_keys($analysis)
                ]);

                // Standardwerte setzen
                $analysis['recommended_status'] = $analysis['recommended_status'] ?? 'pending';
                $analysis['rejection_reasons'] = $analysis['rejection_reasons'] ?? [];
                $analysis['quality_score'] = $analysis['quality_score'] ?? 0;
                $analysis['strengths'] = $analysis['strengths'] ?? [];
                $analysis['weaknesses'] = $analysis['weaknesses'] ?? [];
            }

            // Set status and reasons based on the analysis
            $this->applicationStatus = $analysis['recommended_status'];
            $this->selectedReasons = $analysis['rejection_reasons'];

            // Generate response
            $this->generatedResponse = $this->responseGenerator->generateSmartResponse($this->application);

            // Validiere die generierte Antwort
            if (!isset($this->generatedResponse['markdown']) || !isset($this->generatedResponse['copyable'])) {
                Log::warning('ResponseGenerator: Unvollständige Smart-Antwort', [
                    'application_id' => $this->applicationId,
                    'response_keys' => array_keys($this->generatedResponse)
                ]);

                // Stelle sicher, dass zumindest leere Werte vorhanden sind
                $this->generatedResponse['markdown'] = $this->generatedResponse['markdown'] ?? '';
                $this->generatedResponse['copyable'] = $this->generatedResponse['copyable'] ?? '';
                $this->generatedResponse['suggested_reasons'] = $this->generatedResponse['suggested_reasons'] ?? [];
            }

            // Save application quality and strengths/weaknesses
            $this->applicationQuality = $analysis['quality_score'];
            $this->applicationStrengths = $analysis['strengths'];
            $this->applicationWeaknesses = $analysis['weaknesses'];

            // Generiere Discord-Antwort, wenn Discord-Bot online ist
            if ($this->discordBotStatus) {
                $this->generateDiscordPreview();
            }

            // Signal erfolgreiche Verarbeitung
            $this->processingState = 'success';

            // Stelle sicher, dass alle erforderlichen Schlüssel vorhanden sind
            if (!isset($this->generatedResponse['markdown'])) {
                $this->generatedResponse['markdown'] = '';
            }
            if (!isset($this->generatedResponse['copyable'])) {
                $this->generatedResponse['copyable'] = '';
            }
            if (!isset($this->generatedResponse['discord'])) {
                $this->generatedResponse['discord'] = '';
            }
            if (!isset($this->generatedResponse['suggested_reasons'])) {
                $this->generatedResponse['suggested_reasons'] = [];
            }

            // Log für Debugging
            Log::debug('ResponseGenerator: Smart-Antwort generiert', [
                'markdown_length' => strlen($this->generatedResponse['markdown']),
                'copyable_length' => strlen($this->generatedResponse['copyable']),
                'discord_length' => isset($this->generatedResponse['discord']) ? strlen($this->generatedResponse['discord']) : 0,
                'processing_state' => $this->processingState
            ]);

            // Dispatch Events, um die Antwort anzuzeigen und die Tabs zu initialisieren
            $this->dispatch('smart-response-generated', status: $this->applicationStatus);
            $this->dispatch('response-generated');
            $this->dispatch('response-updated');

            // Verzögerung hinzufügen, um sicherzustellen, dass die Komponente vollständig gerendert ist
            usleep(100000); // 100ms Verzögerung
            $this->dispatch('response-component-rendered');

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler bei der Smart-Antwortgenerierung', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Fehler bei der automatischen Antwortgenerierung: ' . $e->getMessage());
            $this->processingState = 'error';
        }
    }

    /**
     * Toggle a rejection reason
     */
    public function toggleReason($reason)
    {
        try {
            Log::info('ResponseGenerator: Ablehnungsgrund umschalten', [
                'reason' => $reason,
                'application_id' => $this->applicationId
            ]);

            if (in_array($reason, $this->selectedReasons)) {
                // Entfernen unter Beibehaltung der Array-Indizes und anschließendem Neuindizieren
                $this->selectedReasons = array_values(array_diff($this->selectedReasons, [$reason]));
                $this->dispatch('reason-removed', reason: $reason);
            } else {
                $this->selectedReasons[] = $reason;
                $this->dispatch('reason-added', reason: $reason);
            }

            // Regenerate the response
            $this->generateResponse();
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Umschalten des Ablehnungsgrunds', [
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            session()->flash('error', 'Fehler beim Aktualisieren der Ablehnungsgründe.');
        }
    }

    /**
     * Add a suggested reason to the selected reasons
     */
    public function addSuggestedReason($reason)
    {
        try {
            if (!in_array($reason, $this->selectedReasons)) {
                Log::info('ResponseGenerator: Vorgeschlagenen Grund hinzufügen', [
                    'reason' => $reason,
                    'application_id' => $this->applicationId
                ]);

                $this->selectedReasons[] = $reason;
                $this->dispatch('reason-added', reason: $reason);
                $this->generateResponse();
            }
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Hinzufügen des vorgeschlagenen Grunds', [
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            session()->flash('error', 'Fehler beim Hinzufügen des vorgeschlagenen Grunds.');
        }
    }

    /**
     * Copy the response to the clipboard
     */
    public function copyToClipboard($type = 'copyable')
    {
        try {
            if (!isset($this->generatedResponse[$type]) || empty($this->generatedResponse[$type])) {
                Log::warning('ResponseGenerator: Leere oder nicht vorhandene Antwort beim Kopieren', [
                    'application_id' => $this->applicationId,
                    'type' => $type,
                    'available_keys' => array_keys($this->generatedResponse)
                ]);
                session()->flash('error', 'Keine Antwort zum Kopieren verfügbar.');
                return;
            }

            Log::info('ResponseGenerator: Antwort wird in die Zwischenablage kopiert', [
                'application_id' => $this->applicationId,
                'type' => $type,
                'length' => strlen($this->generatedResponse[$type])
            ]);

            $this->dispatch('copyToClipboard', text: $this->generatedResponse[$type]);
            $this->dispatch('response-copied', type: $type);
            session()->flash('clipboard', 'Antwort wurde in die Zwischenablage kopiert.');
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Kopieren in die Zwischenablage', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            session()->flash('error', 'Fehler beim Kopieren der Antwort.');
        }
    }

    /**
     * Update the application status
     */
    public function updatedApplicationStatus()
    {
        try {
            Log::info('ResponseGenerator: Bewerbungsstatus aktualisiert', [
                'application_id' => $this->applicationId,
                'new_status' => $this->applicationStatus
            ]);

            $this->dispatch('status-changed', status: $this->applicationStatus);
            $this->generateResponse();
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Aktualisieren des Bewerbungsstatus', [
                'new_status' => $this->applicationStatus,
                'error' => $e->getMessage()
            ]);
            session()->flash('error', 'Fehler beim Aktualisieren des Bewerbungsstatus.');
        }
    }

    /**
     * Aktualisiert die benutzerdefinierten Gründe und generiert eine neue Antwort
     */
    public function updatedCustomReasons()
    {
        try {
            if (strlen($this->customReasons) > 10) {
                Log::info('ResponseGenerator: Benutzerdefinierte Gründe aktualisiert', [
                    'application_id' => $this->applicationId,
                    'length' => strlen($this->customReasons)
                ]);

                $this->generateResponse();
            }
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Aktualisieren der benutzerdefinierten Gründe', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Render the component
     */
    /**
     * Prüft den Status des Discord-Bots
     */
    public function checkDiscordBotStatus()
    {
        try {
            $this->discordBotStatus = $this->discordService->isBotRunning();

            Log::debug('ResponseGenerator: Discord-Bot-Status geprüft', [
                'status' => $this->discordBotStatus ? 'online' : 'offline'
            ]);

            return $this->discordBotStatus;
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Prüfen des Discord-Bot-Status', [
                'error' => $e->getMessage()
            ]);

            $this->discordBotStatus = false;
            return false;
        }
    }

    /**
     * Sendet die generierte Antwort an den Bewerber via Discord mit verbessertem Error Handling
     */
    public function sendDiscordMessage()
    {
        try {
            $this->processingState = 'processing';
            $this->dispatch('processing-started');

            if (!$this->application) {
                session()->flash('error', 'Keine Bewerbung ausgewählt.');
                $this->processingState = 'error';
                $this->discordMessageStatus = 'error';
                return;
            }

            // Enhanced bot status check with detailed feedback
            $botStatus = $this->discordService->getBotStatus();
            if (!$botStatus['online']) {
                $errorMessage = 'Discord-Bot ist nicht erreichbar.';

                if (isset($botStatus['connection_quality']) && $botStatus['connection_quality'] === 'poor') {
                    $errorMessage .= ' Die Verbindung zum Bot ist instabil.';
                } elseif (isset($botStatus['error'])) {
                    $errorMessage .= ' Fehler: ' . $botStatus['error'];
                }

                $errorMessage .= ' Bitte überprüfen Sie den Bot-Status oder kontaktieren Sie einen Administrator.';

                session()->flash('error', $errorMessage);
                $this->processingState = 'error';
                $this->discordMessageStatus = 'bot_offline';
                return;
            }

            // Stelle sicher, dass wir eine Discord-Antwort haben
            if (empty($this->generatedResponse['discord'])) {
                // Wenn keine Discord-Antwort vorhanden ist, generiere sie
                $discordResponse = $this->responseGenerator->generateDiscordMarkdownResponse(
                    $this->application,
                    $this->applicationStatus,
                    $this->selectedReasons,
                    $this->customReasons
                );

                // Entferne übermäßige Leerzeilen (mehr als 2 aufeinanderfolgende Zeilenumbrüche)
                $this->generatedResponse['discord'] = preg_replace('/\n{3,}/', "\n\n", $discordResponse);
            }

            // Determine roles to assign if approved
            $rolesToAssign = [];
            $roleAssignmentMessage = '';

            if ($this->applicationStatus === 'approved') {
                if (!$this->application->discord_id) {
                    Log::error('Keine Discord ID für Bewerbung vorhanden (Rollenvergabe übersprungen)', [
                        'application_id' => $this->application->id
                    ]);
                    // Decide if this is a hard error or just a warning. For now, proceed with message sending.
                    // session()->flash('warning', 'Keine Discord ID für den Benutzer, Rollen können nicht zugewiesen werden.');
                } else {
                    // Logic from assignRolesToApplicant to determine roles
                    $discordRoleIds = [];
                    $discordRoleIds = array_merge($discordRoleIds, [
                        '1031205205934604439',  // ANGENOMMEN Prod
                        '1335685797080465428'   // ANGENOMMEN Dev
                    ]);

                    $roleMapping = [
                        'actor' => ['1183491860304494642', '1335685797055041541'],
                        'actor_no_voice' => ['1183491860304494642', '1335685797055041541'],
                        'builder' => ['1033491844715257866', '1335685797055041544'],
                        'designer' => ['1033523833514242068', '1335685797067755581'],
                        'voice_actor' => ['1033474411963109466', '1335685797055041539'],
                        'modeler' => ['1041839025696292885', '1335685797067755582'],
                        'developer' => ['1033491763144433724', '1335685797067755586'],
                        'cameraman' => ['1033491696144621658', '1335685797067755584'],
                        'cutter' => ['1033491854995505213', '1335685797067755585']
                    ];

                    if (is_array($this->application->professions) && !empty($this->application->professions)) {
                        foreach ($this->application->professions as $profession) {
                            if (isset($roleMapping[$profession])) {
                                $discordRoleIds = array_merge($discordRoleIds, $roleMapping[$profession]);
                            }
                        }
                    } elseif (!empty($this->application->type) && isset($roleMapping[$this->application->type])) {
                        $discordRoleIds = array_merge($discordRoleIds, $roleMapping[$this->application->type]);
                    }
                    $rolesToAssign = array_values(array_unique(array_filter($discordRoleIds)));
                }
            }

            // Enhanced retry logic for Discord message sending
            $maxRetries = 3;
            $attempt = 0;
            $result = null;

            do {
                $attempt++;

                if ($attempt > 1) {
                    Log::info("Discord message retry attempt {$attempt} for application {$this->applicationId}");
                    sleep(pow(2, $attempt - 2)); // exponential backoff
                }

                // Sende die Nachricht und ggf. Rollen
                $result = $this->discordService->sendApplicationResponse(
                    $this->application,
                    $this->generatedResponse['discord'],
                    $rolesToAssign // Pass roles here
                );

                if ($result['success']) {
                    break; // Success, exit retry loop
                }

                if (isset($result['error_type']) && ($result['error_type'] === 'configuration_error' || $result['error_type'] === 'bot_not_ready')) {
                    Log::warning("Discord service returned non-retryable error from ResponseGenerator's perspective.", [
                        'application_id' => $this->applicationId,
                        'error_type' => $result['error_type'],
                        'message' => $result['message'] ?? 'N/A'
                    ]);
                    break; // Don't retry configuration errors or if bot is persistently not ready
                }

            } while ($attempt < $maxRetries && !$result['success']);

            if ($result['success']) {
                $this->discordMessageSent = true;
                $this->discordMessageStatus = 'success';

                // Aktualisiere den Bewerbungsstatus in der Datenbank
                $this->application->status = $this->applicationStatus;
                $this->application->reviewer_id = Auth::id();
                $this->application->reviewed_at = now();
                $this->application->save();

                $successMessage = 'Nachricht erfolgreich an den Bewerber gesendet.';
                if ($attempt > 1) {
                    $successMessage .= " (nach {$attempt} Versuchen)";
                }

                if (!empty($rolesToAssign)) {
                    // Check if roles were actually assigned by the bot (bot response might indicate this)
                    // For now, assume success if message send was successful
                    $assignedRolesCount = $result['data']['roles_added_count'] ?? count($rolesToAssign); // Example key
                    if ($assignedRolesCount > 0) {
                         $roleAssignmentMessage = "{$assignedRolesCount} Rolle(n) erfolgreich zugewiesen.";
                         Log::info('ResponseGenerator: Rollen wurden dem Bewerber im selben Request zugewiesen', [
                            'application_id' => $this->applicationId,
                            'user_id' => $this->application->user_id,
                            'roles_assigned_count' => $assignedRolesCount
                        ]);
                    } else if (isset($result['data']['roles_failed_count']) && $result['data']['roles_failed_count'] > 0) {
                        $roleAssignmentMessage = "Nachricht gesendet, aber es gab Probleme bei der Rollenzuweisung.";
                         Log::warning('ResponseGenerator: Probleme bei der Rollenzuweisung im selben Request', [
                            'application_id' => $this->applicationId,
                            'user_id' => $this->application->user_id,
                            'response_data' => $result['data']
                        ]);
                    }
                }

                if (!empty($roleAssignmentMessage)) {
                    $successMessage .= ' ' . $roleAssignmentMessage;
                }
                session()->flash('message', $successMessage);
                $this->processingState = 'success';

                // Benachrichtige andere Komponenten über die Änderung
                $this->dispatch('application-status-updated', id: $this->application->id, status: $this->applicationStatus);
            } else {
                $this->discordMessageStatus = 'error';
                session()->flash('error', 'Fehler beim Senden der Nachricht: ' . ($result['message'] ?? 'Unbekannter Fehler'));
                $this->processingState = 'error';
            }

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Senden der Discord-Nachricht', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->discordMessageStatus = 'error';
            session()->flash('error', 'Fehler beim Senden der Discord-Nachricht: ' . $e->getMessage());
            $this->processingState = 'error';
        }
    }

    /**
     * Schnelle Annahme der Bewerbung mit einem Klick
     */
    public function quickApprove()
    {
        try {
            if (!$this->application) {
                session()->flash('error', 'Keine Bewerbung ausgewählt.');
                return;
            }

            // Setze Status auf 'approved'
            $this->applicationStatus = 'approved';

            // Generiere Smart-Antwort
            $this->generateSmartResponse();

            // Wenn Discord-Bot online ist, aktiviere die Option zum Senden
            if ($this->discordBotStatus) {
                $this->sendToDiscord = true;
            }

            session()->flash('message', 'Bewerbung zur Annahme vorbereitet. Überprüfen Sie die Antwort und senden Sie sie ab.');

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler bei der schnellen Annahme', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage()
            ]);

            session()->flash('error', 'Fehler bei der schnellen Annahme: ' . $e->getMessage());
        }
    }

    /**
     * Schnelle Ablehnung der Bewerbung mit einem Klick
     */
    public function quickReject()
    {
        try {
            if (!$this->application) {
                session()->flash('error', 'Keine Bewerbung ausgewählt.');
                return;
            }

            // Setze Status auf 'rejected'
            $this->applicationStatus = 'rejected';

            // Generiere Smart-Antwort
            $this->generateSmartResponse();

            // Wenn Discord-Bot online ist, aktiviere die Option zum Senden
            if ($this->discordBotStatus) {
                $this->sendToDiscord = true;
            }

            session()->flash('message', 'Bewerbung zur Ablehnung vorbereitet. Überprüfen Sie die Antwort und senden Sie sie ab.');

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler bei der schnellen Ablehnung', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage()
            ]);

            session()->flash('error', 'Fehler bei der schnellen Ablehnung: ' . $e->getMessage());
        }
    }

    // Method assignRolesToApplicant() is removed as its logic is now integrated into sendDiscordMessage()

    /**
     * Generiert eine Discord-Vorschau für die Antwort
     */
    public function generateDiscordPreview()
    {
        try {
            if (!$this->application) {
                return;
            }

            // Generiere Discord-Antwort immer neu, um sicherzustellen, dass sie aktuell ist
            $discordResponse = $this->responseGenerator->generateDiscordMarkdownResponse(
                $this->application,
                $this->applicationStatus,
                $this->selectedReasons,
                $this->customReasons
            );

            // Entferne übermäßige Leerzeilen (mehr als 2 aufeinanderfolgende Zeilenumbrüche)
            $this->generatedResponse['discord'] = preg_replace('/\n{3,}/', "\n\n", $discordResponse);

            Log::debug('ResponseGenerator: Discord-Vorschau generiert', [
                'application_id' => $this->applicationId,
                'length' => strlen($this->generatedResponse['discord'])
            ]);

            // Stelle sicher, dass alle erforderlichen Schlüssel vorhanden sind
            if (!isset($this->generatedResponse['markdown'])) {
                $this->generatedResponse['markdown'] = '';
            }
            if (!isset($this->generatedResponse['copyable'])) {
                $this->generatedResponse['copyable'] = '';
            }
            if (!isset($this->generatedResponse['suggested_reasons'])) {
                $this->generatedResponse['suggested_reasons'] = [];
            }

            // Dispatch Events, um die Antwort anzuzeigen und die Tabs zu initialisieren
            $this->dispatch('response-updated');

            // Verzögerung hinzufügen, um sicherzustellen, dass die Komponente vollständig gerendert ist
            usleep(100000); // 100ms Verzögerung
            $this->dispatch('response-component-rendered');

        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler bei der Generierung der Discord-Vorschau', [
                'application_id' => $this->applicationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Render the component
     */
    public function render()
    {
        try {
            // Stelle sicher, dass die generatedResponse immer ein gültiges Array ist
            if (!is_array($this->generatedResponse)) {
                $this->generatedResponse = [
                    'markdown' => $this->generatedResponse ?: '',
                    'copyable' => '',
                    'discord' => '',
                    'suggested_reasons' => []
                ];

                Log::warning('ResponseGenerator: generatedResponse wurde korrigiert');
            }

            // Stelle sicher, dass alle erforderlichen Schlüssel vorhanden sind
            if (!isset($this->generatedResponse['markdown'])) {
                $this->generatedResponse['markdown'] = '';
            }
            if (!isset($this->generatedResponse['copyable'])) {
                $this->generatedResponse['copyable'] = '';
            }
            if (!isset($this->generatedResponse['discord'])) {
                $this->generatedResponse['discord'] = '';
            }
            if (!isset($this->generatedResponse['suggested_reasons'])) {
                $this->generatedResponse['suggested_reasons'] = [];
            }

            // Prüfe Discord-Bot-Status regelmäßig
            if (!$this->discordBotStatus) {
                $this->checkDiscordBotStatus();
            }

            // Dispatch ein Event, um die Tabs zu initialisieren
            $this->dispatch('response-component-rendered');

            // Log für Debugging
            Log::debug('ResponseGenerator: Rendering mit Antwort', [
                'markdown_length' => strlen($this->generatedResponse['markdown']),
                'copyable_length' => strlen($this->generatedResponse['copyable']),
                'discord_length' => strlen($this->generatedResponse['discord']),
                'processing_state' => $this->processingState
            ]);

            return view('livewire.response-generator', [
                'isProcessing' => $this->processingState === 'processing' || $this->processingState === 'loading',
                'isError' => $this->processingState === 'error',
                'isSuccess' => $this->processingState === 'success',
                'discordBotStatus' => $this->discordBotStatus
            ]);
        } catch (\Exception $e) {
            Log::error('ResponseGenerator: Fehler beim Rendern', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return view('livewire.response-generator', [
                'isProcessing' => false,
                'isError' => true,
                'isSuccess' => false
            ]);
        }
    }
}
