# MineWache Website - Production Testing Script for Windows
# This script tests the production environment to ensure all components are functioning correctly

# Function to display colored messages
function Write-ColorMessage {
    param (
        [string]$color,
        [string]$message
    )

    switch ($color) {
        "green" { Write-Host $message -ForegroundColor Green }
        "yellow" { Write-Host $message -ForegroundColor Yellow }
        "red" { Write-Host $message -ForegroundColor Red }
        "blue" { Write-Host $message -ForegroundColor Blue }
        default { Write-Host $message }
    }
}

# Function to log messages
function Log-Message {
    param (
        [string]$level,
        [string]$message
    )

    $logFile = ".\test-production.log"

    # Write to log file
    "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] [$level] $message" | Out-File -Append -FilePath $logFile

    # Display to console
    Write-ColorMessage -color $level -message $message
}

# Function to test web server
function Test-WebServer {
    Log-Message -level "blue" -message "Testing web server..."

    $appUrl = php artisan config:get app.url
    if (-not $appUrl) {
        $appUrl = "http://localhost"
        Log-Message -level "yellow" -message "Could not determine app URL from config. Using default: $appUrl"
    }

    try {
        $response = Invoke-WebRequest -Uri $appUrl -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Log-Message -level "green" -message "Web server is running and responding with status code 200."
            return $true
        } else {
            Log-Message -level "red" -message "Web server responded with unexpected status code: $($response.StatusCode)"
            return $false
        }
    } catch {
        Log-Message -level "red" -message "Failed to connect to web server: $_"
        return $false
    }
}

# Function to test database connection
function Test-Database {
    Log-Message -level "blue" -message "Testing database connection..."

    try {
        $result = php artisan db:show --json
        if ($result) {
            Log-Message -level "green" -message "Database connection successful."
            return $true
        } else {
            Log-Message -level "red" -message "Database connection test returned empty result."
            return $false
        }
    } catch {
        Log-Message -level "red" -message "Database connection test failed: $_"
        return $false
    }
}

# Function to test Redis connection
function Test-Redis {
    Log-Message -level "blue" -message "Testing Redis connection..."

    # Check if Redis is configured
    $redisHost = php artisan config:get database.redis.default.host
    $redisPort = php artisan config:get database.redis.default.port

    if (-not $redisHost -or -not $redisPort) {
        Log-Message -level "yellow" -message "Redis configuration not found. Skipping Redis test."
        return $true
    }

    # Test Redis connection using Laravel
    try {
        $result = php artisan redis:ping
        if ($result -match "PONG") {
            Log-Message -level "green" -message "Redis connection successful."
            return $true
        } else {
            Log-Message -level "red" -message "Redis connection test failed: Unexpected response."
            return $false
        }
    } catch {
        Log-Message -level "red" -message "Redis connection test failed: $_"
        return $false
    }
}

# Function to test queue worker
function Test-QueueWorker {
    Log-Message -level "blue" -message "Testing queue worker..."

    # Check if queue worker is running
    $queueWorkerProcesses = Get-Process | Where-Object { $_.CommandLine -match "artisan queue:work" }

    if ($queueWorkerProcesses) {
        Log-Message -level "green" -message "Queue worker is running."

        # Test queue by dispatching a test job
        try {
            php artisan queue:test-job
            Log-Message -level "green" -message "Test job dispatched to queue."
            return $true
        } catch {
            Log-Message -level "yellow" -message "Could not dispatch test job: $_"
            Log-Message -level "yellow" -message "Queue worker is running, but job dispatch test failed."
            return $true
        }
    } else {
        Log-Message -level "red" -message "Queue worker is not running."
        return $false
    }
}

# Function to test Reverb WebSocket server
function Test-Reverb {
    Log-Message -level "blue" -message "Testing Reverb WebSocket server..."

    # Check if Reverb is configured
    $reverbHost = php artisan config:get reverb.server.host
    $reverbPort = php artisan config:get reverb.server.port

    if (-not $reverbHost -or -not $reverbPort) {
        Log-Message -level "yellow" -message "Reverb configuration not found. Skipping Reverb test."
        return $true
    }

    # Check if Reverb process is running
    $reverbProcesses = Get-Process | Where-Object { $_.CommandLine -match "artisan reverb:start" }

    if ($reverbProcesses) {
        Log-Message -level "green" -message "Reverb WebSocket server is running."
        return $true
    } else {
        Log-Message -level "red" -message "Reverb WebSocket server is not running."
        return $false
    }
}

# Function to test Discord bot
function Test-DiscordBot {
    Log-Message -level "blue" -message "Testing Discord bot..."

    # Check if Discord bot is configured
    $discordBotToken = php artisan config:get minewache_discord_bot.token

    if (-not $discordBotToken) {
        Log-Message -level "yellow" -message "Discord bot token not found. Skipping Discord bot test."
        return $true
    }

    # Check if Discord bot process is running
    $discordBotProcesses = Get-Process | Where-Object { $_.CommandLine -match "artisan minewache:run-discord-bot" -or $_.CommandLine -match "discord-bot/index.js" }

    if ($discordBotProcesses) {
        Log-Message -level "green" -message "Discord bot is running."
        return $true
    } else {
        Log-Message -level "red" -message "Discord bot is not running."
        return $false
    }
}

# Function to test asset compilation
function Test-AssetCompilation {
    Log-Message -level "blue" -message "Testing asset compilation..."

    # Check if compiled assets exist
    if (Test-Path "public\build\manifest.json") {
        Log-Message -level "green" -message "Compiled assets found."
        return $true
    } else {
        Log-Message -level "red" -message "Compiled assets not found. Run 'npm run build' to compile assets."
        return $false
    }
}

# Function to test cache configuration
function Test-CacheConfiguration {
    Log-Message -level "blue" -message "Testing cache configuration..."

    # Check if config is cached
    if (Test-Path "bootstrap\cache\config.php") {
        Log-Message -level "green" -message "Configuration cache found."
    } else {
        Log-Message -level "yellow" -message "Configuration cache not found. Run 'php artisan config:cache' for better performance."
    }

    # Check if routes are cached
    if (Test-Path "bootstrap\cache\routes-v7.php") {
        Log-Message -level "green" -message "Route cache found."
    } else {
        Log-Message -level "yellow" -message "Route cache not found. Run 'php artisan route:cache' for better performance."
    }

    # Check if views are cached
    $viewCacheFiles = Get-ChildItem -Path "storage\framework\views" -Filter "*.php" | Measure-Object
    if ($viewCacheFiles.Count -gt 0) {
        Log-Message -level "green" -message "View cache found."
    } else {
        Log-Message -level "yellow" -message "View cache not found. Run 'php artisan view:cache' for better performance."
    }

    return $true
}

# Function to test environment configuration
function Test-EnvironmentConfiguration {
    Log-Message -level "blue" -message "Testing environment configuration..."

    # Check if .env file exists
    if (-not (Test-Path ".env")) {
        Log-Message -level "red" -message ".env file not found. Create one from .env.example."
        return $false
    }

    # Check if APP_ENV is set to production
    $appEnv = php artisan config:get app.env
    if ($appEnv -ne "production") {
        Log-Message -level "red" -message "APP_ENV is not set to production. Current value: $appEnv"
        return $false
    }

    # Check if APP_DEBUG is set to false
    $appDebug = php artisan config:get app.debug
    if ($appDebug -ne "false") {
        Log-Message -level "red" -message "APP_DEBUG is not set to false. Current value: $appDebug"
        return $false
    }

    # Check if APP_KEY is set
    $appKey = php artisan config:get app.key
    if (-not $appKey) {
        Log-Message -level "red" -message "APP_KEY is not set. Run 'php artisan key:generate'."
        return $false
    }

    Log-Message -level "green" -message "Environment configuration is correct for production."
    return $true
}

# Main function to run all tests
function Test-ProductionEnvironment {
    Log-Message -level "blue" -message "Starting production environment tests..."

    $allTestsPassed = $true

    # Run all tests
    $allTestsPassed = $allTestsPassed -and (Test-EnvironmentConfiguration)
    $allTestsPassed = $allTestsPassed -and (Test-WebServer)
    $allTestsPassed = $allTestsPassed -and (Test-Database)
    $allTestsPassed = $allTestsPassed -and (Test-Redis)
    $allTestsPassed = $allTestsPassed -and (Test-QueueWorker)
    $allTestsPassed = $allTestsPassed -and (Test-Reverb)
    $allTestsPassed = $allTestsPassed -and (Test-DiscordBot)
    $allTestsPassed = $allTestsPassed -and (Test-AssetCompilation)
    $allTestsPassed = $allTestsPassed -and (Test-CacheConfiguration)

    if ($allTestsPassed) {
        Log-Message -level "green" -message "All production environment tests passed!"
    } else {
        Log-Message -level "red" -message "Some production environment tests failed. Check the log for details."
    }
}

# Run the tests
Test-ProductionEnvironment
