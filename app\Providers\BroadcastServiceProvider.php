<?php

namespace App\Providers;

use App\Broadcasting\CustomReverbBroadcaster;
use App\Enums\Role;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Broadcasting\Broadcasters\ReverbBroadcaster;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Override the default broadcaster to ensure Reverb is used
        $this->app->singleton('Illuminate\Broadcasting\BroadcastManager', function ($app) {
            $manager = new \Illuminate\Broadcasting\BroadcastManager($app);

            // Register the Reverb broadcaster extension with our custom implementation
            $manager->extend('reverb', function ($app, $config) {
                $broadcaster = new CustomReverbBroadcaster(
                    $app->make('reverb.client'),
                    $app['config']->get('app.debug')
                );

                // Add a custom authorizer for API token authentication
                $broadcaster->resolveAuthorizationCallback(function ($request, $channel) {
                    // For public channels, always allow access
                    if (!str_starts_with($channel, 'private-') && !str_starts_with($channel, 'presence-')) {
                        return true;
                    }

                    // Check if this is an API token request (Discord bot)
                    $token = $request->bearerToken() ?? $request->input('api_token');
                    $configToken = config('services.discord.api_key');
                    if ($token && $token === $configToken) {
                        Log::info('Authorizing API token access to channel', [
                            'channel' => $channel,
                        ]);
                        return true;
                    }

                    // For private and presence channels, the user must be authenticated
                    if (!$request->user()) {
                        Log::warning('Unauthenticated user trying to access private channel', [
                            'channel' => $channel,
                            'ip' => $request->ip(),
                        ]);
                        return false;
                    }

                    // Use the channel authorization logic in channels.php
                    return null;
                });

                return $broadcaster;
            });

            return $manager;
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure broadcasting routes with proper middleware
        // For web authentication (session-based)
        Broadcast::routes(['middleware' => ['web', 'auth']]);

        // For API authentication (token-based)
        Broadcast::routes(['middleware' => ['api.token']], 'api');

        // Register the channel authorization routes
        require base_path('routes/channels.php');

        // Log when the BroadcastServiceProvider is booted, conditionally
        if (config('app.debug', false)) {
            Log::info('BroadcastServiceProvider booted with updated configuration');
        }
    }
}
