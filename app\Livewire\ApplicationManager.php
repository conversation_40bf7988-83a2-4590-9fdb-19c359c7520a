<?php

namespace App\Livewire;

use Livewire\Component;

class ApplicationManager extends Component
{
    public $viewMode = 'list'; // 'list', 'detail', 'edit', 'response'
    public $applicationId = null;

    protected $listeners = [
        'showApplicationDetail' => 'showDetail',
        'showApplicationEdit' => 'showEdit',
        'showResponseGenerator' => 'showResponseGenerator',
        'backToList' => 'backToList',
        'refreshApplications' => '$refresh'
    ];

    /**
     * Show the detail view for an application
     */
    public function showDetail($id)
    {
        $this->viewMode = 'detail';
        $this->applicationId = $id;
    }

    /**
     * Show the edit view for an application
     */
    public function showEdit($id)
    {
        $this->viewMode = 'edit';
        $this->applicationId = $id;
    }

    /**
     * Show the response generator for an application
     */
    public function showResponseGenerator($id)
    {
        $this->viewMode = 'response';
        $this->applicationId = $id;
    }

    /**
     * Go back to the list view
     */
    public function backToList()
    {
        $this->viewMode = 'list';
        $this->applicationId = null;
    }

    /**
     * Render the appropriate view based on the current mode
     */
    public function render()
    {
        return view('livewire.application-manager');
    }
}
