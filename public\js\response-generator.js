// Response Generator Tab Functionality
function initResponseGeneratorTabs() {
    console.log('Initializing ResponseGenerator tabs');

    // Tabs Functionality
    const tabs = document.querySelectorAll('.tabs .tab');
    const tabContents = document.querySelectorAll('[data-tab-content]');

    if (tabs.length === 0 || tabContents.length === 0) {
        console.log('No tabs or tab contents found');
        return;
    }

    console.log(`Found ${tabs.length} tabs and ${tabContents.length} tab contents`);

    // Add event listeners to the tabs
    tabs.forEach(tab => {
        if (tab.getAttribute('data-tab-listener-attached') === 'true') {
            // Listener already attached, skip
            return;
        }

        tab.addEventListener('click', () => {
            const target = tab.getAttribute('data-tab');
            console.log(`Tab clicked: ${target}`);

            // Update active tab
            tabs.forEach(t => t.classList.remove('tab-active'));
            tab.classList.add('tab-active');

            // Show target content
            tabContents.forEach(content => {
                if (content.getAttribute('data-tab-content') === target) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });

            // Special handling for Discord tab
            if (target === 'discord') {
                // Check if we need to generate Discord preview
                const discordContent = document.querySelector('[data-tab-content="discord"]');
                const discordResponse = discordContent.textContent.trim();
                if (!discordResponse || discordResponse.includes('Keine Discord-Antwort generiert')) {
                    console.log('Generating Discord preview');
                    // Use Livewire's dispatch method to trigger the generateDiscordPreview method
                    if (window.Livewire) {
                        window.Livewire.dispatch('generateDiscordPreview');
                    }
                }
            }
        });

        tab.setAttribute('data-tab-listener-attached', 'true'); // Mark as listener attached
    });

    // Ensure the preview tab is active and visible by default
    const previewTab = document.getElementById('tab-preview');
    const previewContent = document.getElementById('content-preview');

    if (previewTab && previewContent) {
        // Activate the preview tab
        tabs.forEach(t => t.classList.remove('tab-active'));
        previewTab.classList.add('tab-active');

        // Show the preview content
        tabContents.forEach(content => {
            if (content.getAttribute('data-tab-content') === 'preview') {
                content.classList.remove('hidden');
            } else {
                content.classList.add('hidden');
            }
        });

        console.log('Preview tab activated by default');
    }

    // Activate first tab by default if no tab is active
    let activeTab = Array.from(tabs).find(tab => tab.classList.contains('tab-active'));
    if (!activeTab && tabs.length > 0) {
        // Manually trigger the click event on the first tab
        const firstTab = tabs[0];
        firstTab.classList.add('tab-active');

        // Show the first tab content and hide others
        const firstTabTarget = firstTab.getAttribute('data-tab');
        tabContents.forEach(content => {
            if (content.getAttribute('data-tab-content') === firstTabTarget) {
                content.classList.remove('hidden');
            } else {
                content.classList.add('hidden');
            }
        });
    }
}

// Funktion, die prüft, ob die Tabs im DOM vorhanden sind und sie initialisiert
function checkAndInitTabs() {
    const tabs = document.querySelectorAll('.tabs .tab');
    const tabContents = document.querySelectorAll('[data-tab-content]');

    if (tabs.length > 0 && tabContents.length > 0) {
        console.log('Tabs found in DOM, initializing...');
        initResponseGeneratorTabs();
        return true;
    }
    return false;
}

// Global flag to prevent multiple polling sessions
let isPolling = false;

// Funktion, die wiederholt prüft, ob die Tabs im DOM vorhanden sind
function pollForTabs(maxAttempts = 10, interval = 200) {
    // Prevent multiple polling sessions
    if (isPolling) {
        console.log('Polling already in progress, skipping');
        return;
    }

    isPolling = true;
    let attempts = 0;

    const poll = setInterval(function() {
        attempts++;
        console.log(`Polling for tabs, attempt ${attempts}`);

        if (checkAndInitTabs() || attempts >= maxAttempts) {
            clearInterval(poll);
            isPolling = false;
            console.log('Polling for tabs completed');
        }
    }, interval);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded, checking for tabs');
    if (!checkAndInitTabs()) {
        console.log('Tabs not found on DOMContentLoaded, starting polling');
        pollForTabs();
    }

    // Manuell den Vorschau-Tab aktivieren
    setTimeout(function() {
        const previewTab = document.getElementById('tab-preview');
        const previewContent = document.getElementById('content-preview');

        if (previewTab && previewContent) {
            // Aktiviere den Vorschau-Tab
            const tabs = document.querySelectorAll('.tabs .tab');
            tabs.forEach(t => t.classList.remove('tab-active'));
            previewTab.classList.add('tab-active');

            // Zeige den Vorschau-Inhalt
            const tabContents = document.querySelectorAll('[data-tab-content]');
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            previewContent.classList.remove('hidden');

            console.log('Preview tab manually activated on DOMContentLoaded');
        }
    }, 300);
});

// Re-initialize when Livewire updates the DOM
document.addEventListener('livewire:navigated', function() {
    console.log('Livewire navigated, checking for tabs');
    checkAndInitTabs();
});

// Re-initialize when Livewire updates a component
document.addEventListener('livewire:update', function() {
    console.log('Livewire update, checking for tabs');
    checkAndInitTabs();
});

// Listen for specific Livewire events
document.addEventListener('livewire:initialized', function() {
    console.log('Livewire initialized, checking for tabs');
    if (!checkAndInitTabs()) {
        console.log('Tabs not found on livewire:initialized, starting polling');
        pollForTabs();
    }
});

// This event is dispatched when the response is updated
document.addEventListener('response-updated', function() {
    console.log('Response updated, checking for tabs');
    setTimeout(function() {
        if (!checkAndInitTabs()) {
            console.log('Tabs not found on response-updated, starting polling');
            pollForTabs(15, 100); // Reduced polling frequency
        } else {
            // Ensure the preview tab is active and visible
            const previewTab = document.getElementById('tab-preview');
            const previewContent = document.getElementById('content-preview');

            if (previewTab && previewContent) {
                // Activate the preview tab
                const tabs = document.querySelectorAll('.tabs .tab');
                tabs.forEach(t => t.classList.remove('tab-active'));
                previewTab.classList.add('tab-active');

                // Show the preview content
                const tabContents = document.querySelectorAll('[data-tab-content]');
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                previewContent.classList.remove('hidden');

                console.log('Preview tab activated after response update');
            }
        }
    }, 100);
});

// This event is dispatched when a response is generated
document.addEventListener('response-generated', function() {
    console.log('Response generated, checking for tabs');
    setTimeout(function() {
        if (!checkAndInitTabs()) {
            console.log('Tabs not found on response-generated, starting polling');
            pollForTabs(15, 100); // Reduced polling frequency
        } else {
            // Ensure the preview tab is active and visible
            const previewTab = document.getElementById('tab-preview');
            const previewContent = document.getElementById('content-preview');

            if (previewTab && previewContent) {
                // Activate the preview tab
                const tabs = document.querySelectorAll('.tabs .tab');
                tabs.forEach(t => t.classList.remove('tab-active'));
                previewTab.classList.add('tab-active');

                // Show the preview content
                const tabContents = document.querySelectorAll('[data-tab-content]');
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                previewContent.classList.remove('hidden');

                console.log('Preview tab activated after response generation');
            }
        }
    }, 100);
});

// Listen for the custom event from the ResponseGenerator component
document.addEventListener('response-component-rendered', function() {
    console.log('Response component rendered event received');
    setTimeout(function() {
        if (checkAndInitTabs()) {
            // Manually show the preview tab content
            const previewContent = document.getElementById('content-preview');
            if (previewContent) {
                previewContent.classList.remove('hidden');
                console.log('Preview content made visible');
            } else {
                console.log('Preview content element not found');
            }

            // Manually set the preview tab as active
            const previewTab = document.getElementById('tab-preview');
            if (previewTab) {
                previewTab.classList.add('tab-active');
                console.log('Preview tab activated');
            } else {
                console.log('Preview tab element not found');
            }

            // Force all other tab contents to be hidden
            const tabContents = document.querySelectorAll('[data-tab-content]');
            tabContents.forEach(content => {
                if (content.getAttribute('data-tab-content') !== 'preview') {
                    content.classList.add('hidden');
                }
            });
        } else {
            console.log('Tabs not found after response-component-rendered, starting polling');
            pollForTabs(10, 150); // Reduced polling frequency
        }
    }, 100); // Small delay to ensure DOM is fully updated
});

// Initialize tabs when the page is loaded
window.addEventListener('load', function() {
    console.log('Window loaded, checking for tabs');
    setTimeout(function() {
        if (checkAndInitTabs()) {
            // Manually show the preview tab content
            const previewContent = document.getElementById('content-preview');
            if (previewContent) {
                previewContent.classList.remove('hidden');
            }

            // Manually set the preview tab as active
            const previewTab = document.getElementById('tab-preview');
            if (previewTab) {
                previewTab.classList.add('tab-active');
            }
        } else {
            console.log('Tabs not found after window load, starting polling');
            pollForTabs(10, 200); // Reduced polling frequency
        }
    }, 300); // Moderate delay to ensure DOM is loaded
});

// MutationObserver to detect when tabs are added to the DOM
document.addEventListener('DOMContentLoaded', function() {
    // Make sure the body element exists before creating the observer
    if (document.body) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes contain our tabs
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.querySelector && (node.querySelector('.tabs .tab') || node.querySelector('[data-tab-content]'))) {
                                console.log('Tabs detected in DOM mutation');
                                checkAndInitTabs();
                                break;
                            }
                        }
                    }
                }
            });
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });
        console.log('MutationObserver initialized and observing document.body');
    } else {
        console.warn('document.body not available yet, MutationObserver not initialized');
    }
});
