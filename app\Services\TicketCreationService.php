<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class TicketCreationService
{
    protected DiscordTicketChannelService $discordTicketChannelService;
    protected TicketService $ticketService;

    public function __construct(
        DiscordTicketChannelService $discordTicketChannelService,
        TicketService $ticketService
    ) {
        $this->discordTicketChannelService = $discordTicketChannelService;
        $this->ticketService = $ticketService;
    }

    /**
     * Creates a new ticket from a Discord interaction.
     *
     * @param string $discordUserId
     * @param string $title
     * @param string $description
     * @param string $guildId
     * @param string $locale
     * @return Ticket|null
     */
    public function createTicketFromDiscord(string $discordUserId, string $title, string $description, string $guildId, string $locale): ?Ticket
    {
        try {
            // Find or create the user associated with the Discord ID
            $user = User::firstOrCreate(
                ['discord_id' => $discordUserId],
                [
                    'name' => 'Discord User ' . $discordUserId,
                    // You might want to fetch the user's actual Discord username
                    // and set it here, but that requires another API call.
                    // This is a safe fallback.
                    'email' => $discordUserId . '@users.discordapp.com', // Placeholder email
                    'password' => bcrypt(Str::random(16)), // Placeholder password
                ]
            );

            // Create the ticket in the database first
            $ticket = Ticket::create([
                'user_id' => $user->id,
                'title' => $title,
                'status' => 'open',
                'priority' => 'medium', // Default priority
                'discord_guild_id' => $guildId,
            ]);

            if (!$ticket) {
                Log::error("Failed to create ticket in database for Discord user {$discordUserId}.");
                return null;
            }

            // Create the initial message for the ticket
            TicketMessage::create([
                'ticket_id' => $ticket->id,
                'user_id' => $user->id,
                'content' => $description,
                'is_system_message' => false,
                'message_source' => 'discord',
            ]);


            // Now create the Discord channel for the ticket
            $channel = $this->discordTicketChannelService->createTicketChannel($guildId, $ticket->id, $title, $discordUserId);

            if ($channel && isset($channel['id'])) {
                // Update the ticket with the new channel ID and Discord user ID
                $ticket->discord_channel_id = $channel['id'];
                $ticket->discord_user_id = $discordUserId; // Store the Discord user ID for permission management
                $ticket->save();

                return $ticket;
            } else {
                Log::error("Failed to create Discord channel for ticket #{$ticket->id}.");
                // Optionally delete the ticket and message here if a channel is mandatory
                $ticket->delete();
                return null;
            }
        } catch (Exception $e) {
            Log::error("Exception in createTicketFromDiscord for user {$discordUserId}: " . $e->getMessage(), [
                'exception' => $e
            ]);
            return null;
        }
    }
}
