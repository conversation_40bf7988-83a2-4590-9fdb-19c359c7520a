/**
 * ----------------------------------------------------------------
 * WebGLManager: Handles low-level WebGL setup and utilities.
 * ----------------------------------------------------------------
 */
class WebGLManager {
    constructor(canvasId) {
        const canvas = document.getElementById(canvasId);
        this.gl = canvas.getContext('webgl', { antialias: true, powerPreference: 'high-performance' });
        if (!this.gl) throw new Error('WebGL not supported');

        this.canvas = canvas;
        this.programs = {};
        this.locations = {};
        this.buffers = {};
        this.textures = {};

        this.init();
    }

    init() {
        this.programs.glass = this._createProgram(this.glassVsSource, this.glassFsSource);
        this.programs.background = this._createProgram(this.backgroundVsSource, this.backgroundFsSource);

        this.locations.glass = this._getGlassLocations();
        this.locations.background = this._getBackgroundLocations();

        this.buffers.quad = this._createBuffer(new Float32Array([-0.5,-0.5, 0.5,-0.5, -0.5,0.5, -0.5,0.5, 0.5,-0.5, 0.5,0.5]));
        this.buffers.screenQuad = this._createBuffer(new Float32Array([-1,-1, 1,-1, -1,1, -1,1, 1,-1, 1,1]));

        this.textures.background = this._createTexture();
        this.loadTexture('https://picsum.photos/1920/1080?random=' + Math.floor(Math.random()*100));
    }

    loadTexture(url, callback) {
        const image = new Image();
        image.crossOrigin = "anonymous";
        image.src = url;
        image.onload = () => {
            const gl = this.gl;
            gl.bindTexture(gl.TEXTURE_2D, this.textures.background);
            gl.pixelStorei(gl.UNPACK_FLIP_Y_WEBGL, true);
            gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
            if (callback) callback();
        };
        image.onerror = () => console.error("Failed to load image:", url);
    }

    drawBackground() {
        const gl = this.gl;
        const program = this.programs.background;
        const locations = this.locations.background;
        const buffer = this.buffers.screenQuad;

        gl.useProgram(program);
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.enableVertexAttribArray(locations.position);
        gl.vertexAttribPointer(locations.position, 2, gl.FLOAT, false, 0, 0);
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, this.textures.background);
        gl.uniform1i(locations.backgroundTexture, 0);
        gl.drawArrays(gl.TRIANGLES, 0, 6);
    }

    _createShader(type, source) { const s = this.gl.createShader(type); this.gl.shaderSource(s, source); this.gl.compileShader(s); if (!this.gl.getShaderParameter(s, this.gl.COMPILE_STATUS)) { console.error(`Shader error:`, this.gl.getShaderInfoLog(s)); this.gl.deleteShader(s); return null; } return s; }
    _createProgram(vs, fs) { const p = this.gl.createProgram(); this.gl.attachShader(p, this._createShader(this.gl.VERTEX_SHADER, vs)); this.gl.attachShader(p, this._createShader(this.gl.FRAGMENT_SHADER, fs)); this.gl.linkProgram(p); if (!this.gl.getProgramParameter(p, this.gl.LINK_STATUS)) { console.error('Program link error:', this.gl.getProgramInfoLog(p)); return null; } return p; }
    _createBuffer(data) { const b = this.gl.createBuffer(); this.gl.bindBuffer(this.gl.ARRAY_BUFFER, b); this.gl.bufferData(this.gl.ARRAY_BUFFER, data, this.gl.STATIC_DRAW); return b; }
    _createTexture() { const t = this.gl.createTexture(); this.gl.bindTexture(this.gl.TEXTURE_2D, t); this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, 1, 1, 0, this.gl.RGBA, this.gl.UNSIGNED_BYTE, new Uint8Array([20, 20, 30, 255])); this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE); this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE); this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR); return t; }
    _getGlassLocations() { const p = this.programs.glass; return { position: this.gl.getAttribLocation(p, 'a_position'), resolution: this.gl.getUniformLocation(p, 'u_resolution'), mousePos: this.gl.getUniformLocation(p, 'u_mousePos'), glassSize: this.gl.getUniformLocation(p, 'u_glassSize'), backgroundTexture: this.gl.getUniformLocation(p, 'u_backgroundTexture'), cornerRadius: this.gl.getUniformLocation(p, 'u_cornerRadius'), ior: this.gl.getUniformLocation(p, 'u_ior'), glassThickness: this.gl.getUniformLocation(p, 'u_glassThickness'), normalStrength: this.gl.getUniformLocation(p, 'u_normalStrength'), displacementScale: this.gl.getUniformLocation(p, 'u_displacementScale'), heightTransitionWidth: this.gl.getUniformLocation(p, 'u_heightTransitionWidth'), sminSmoothing: this.gl.getUniformLocation(p, 'u_sminSmoothing'), showNormals: this.gl.getUniformLocation(p, 'u_showNormals'), blurRadius: this.gl.getUniformLocation(p, 'u_blurRadius'), overlayColor: this.gl.getUniformLocation(p, 'u_overlayColor'), highlightWidth: this.gl.getUniformLocation(p, 'u_highlightWidth'), }; }
    _getBackgroundLocations() { const p = this.programs.background; return { position: this.gl.getAttribLocation(p, 'a_position'), backgroundTexture: this.gl.getUniformLocation(p, 'u_backgroundTexture'), }; }

    get glassVsSource() { return `precision mediump float; attribute vec2 a_position; uniform vec2 u_resolution; uniform vec2 u_mousePos; uniform vec2 u_glassSize; varying vec2 v_screenTexCoord; varying vec2 v_shapeCoord; void main() { vec2 screenPos = u_mousePos + a_position * u_glassSize; vec2 clipSpacePos = (screenPos / u_resolution) * 2.0 - 1.0; gl_Position = vec4(clipSpacePos * vec2(1.0, -1.0), 0.0, 1.0); v_screenTexCoord = screenPos / u_resolution; v_screenTexCoord.y = 1.0 - v_screenTexCoord.y; v_shapeCoord = a_position; }`; }
    get glassFsSource() { return `precision mediump float;

uniform sampler2D u_backgroundTexture;
uniform vec2 u_resolution;
uniform vec2 u_glassSize;
uniform float u_cornerRadius;
uniform float u_ior;
uniform float u_glassThickness;
uniform float u_normalStrength;
uniform float u_displacementScale;
uniform float u_heightTransitionWidth; // Renamed from u_heightBlurFactor for clarity
uniform float u_sminSmoothing;     // New: SDF smoothing factor k
uniform int u_showNormals;
uniform float u_blurRadius;        // New: Blur radius for frosted glass effect
uniform vec4 u_overlayColor;       // New: Overlay color for the glass (e.g., subtle white)
uniform float u_highlightWidth;    // New: Width of the white highlight at the edge

varying vec2 v_screenTexCoord;
varying vec2 v_shapeCoord;

// Polynomial smooth min (quartic)
// k controls the smoothness/radius of the blend
float smin_polynomial(float a, float b, float k) {
    if (k <= 0.0) return min(a, b); // Avoid division by zero or no smoothing
    float h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0);
    return mix(b, a, h) - k * h * (1.0 - h);
}

// Polynomial smooth max
float smax_polynomial(float a, float b, float k) {
    if (k <= 0.0) return max(a, b);
    // return -smin_polynomial(-a, -b, k); // Alternative formulation
    float h = clamp(0.5 + 0.5 * (a - b) / k, 0.0, 1.0);
    return mix(b, a, h) + k * h * (1.0 - h); // Note: +k and (a-b)
}

// Original sdRoundedBox (for reference or if k_smooth is 0)
float sdRoundedBoxSharp(vec2 p, vec2 b, float r) {
    vec2 q = abs(p) - b + r;
    return min(max(q.x, q.y), 0.0) + length(max(q, 0.0)) - r;
}

// Smoothed sdRoundedBox using polynomial smin/smax
float sdRoundedBoxSmooth(vec2 p, vec2 b, float r, float k_smooth) {
    if (k_smooth <= 0.0) { // Fallback to sharp if no smoothing
        return sdRoundedBoxSharp(p,b,r);
    }
    vec2 q = abs(p) - b + r;

    // Term A: max(q.x, q.y) - This is a key part for corner definition
    float termA_smooth = smax_polynomial(q.x, q.y, k_smooth);

    // Term B: min(termA_smooth, 0.0) - Clamps the distance for points along straight edges
    // Smoothing this min( , 0.0) can be tricky. Using a smaller k or no smoothing might be safer.
    // Let's try with a potentially smaller k for this specific part.
    float termB_smooth = smin_polynomial(termA_smooth, 0.0, k_smooth * 0.5);

    // Term C: length(max(q, 0.0)) - Distance from corner center for points in corner region
    // max(q, 0.0) is vec2(max(q.x, 0.0), max(q.y, 0.0))
    vec2 q_for_length_smooth = vec2(
        smax_polynomial(q.x, 0.0, k_smooth),
        smax_polynomial(q.y, 0.0, k_smooth)
    );
    float termC_smooth = length(q_for_length_smooth);

    return termB_smooth + termC_smooth - r;
}

// Helper function to convert SDF to height
float getHeightFromSDF(vec2 p_pixel_space, vec2 b_pixel_space, float r_pixel, float k_s, float transition_w) {
    float dist_sample = sdRoundedBoxSmooth(p_pixel_space, b_pixel_space, r_pixel, k_s);
    // Normalize dist_sample to [-1, 1] within the transition band
    float normalized_dist = dist_sample / transition_w;

    // Use a logistic sigmoid function for a steep drop at the edge (normalized_dist=0) and flatten out
    // A higher steepness_factor leads to a sharper transition
    const float steepness_factor = 6.0; // This value can be tuned
    float height = 1.0 - (1.0 / (1.0 + exp(-normalized_dist * steepness_factor)));

    // Clamp to [0, 1] to ensure it stays within valid height range
    return clamp(height, 0.0, 1.0);
}

void main() {
    float actualCornerRadius = min(u_cornerRadius, min(u_glassSize.x, u_glassSize.y) / 2.0);

    // Current point in pixel space relative to glass center
    vec2 current_p_pixel = v_shapeCoord * u_glassSize;
    vec2 glass_half_size_pixel = u_glassSize / 2.0;

    // Initial SDF check for discard (can use sharp version for efficiency if k_smooth is large)
    float dist_for_shape_boundary = sdRoundedBoxSmooth(current_p_pixel, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing);
    if (dist_for_shape_boundary > 0.001) { // Discard if clearly outside transition band
        discard;
    }


    vec2 pixel_step_in_norm_space = vec2(1.0 / u_glassSize.x, 1.0 / u_glassSize.y); // Step in v_shapeCoord's space

    // Sampling steps in normalized shape space (v_shapeCoord space)
    float norm_step_x1 = pixel_step_in_norm_space.x * 0.75;
    float norm_step_y1 = pixel_step_in_norm_space.y * 0.75;
    float norm_step_x2 = pixel_step_in_norm_space.x * 1.5;
    float norm_step_y2 = pixel_step_in_norm_space.y * 1.5;

    // Calculate X direction gradient
    // getHeightFromSDF expects pixel space coords for p, b, r, k_s, transition_w
    float h_px1 = getHeightFromSDF((v_shapeCoord + vec2(norm_step_x1, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_nx1 = getHeightFromSDF((v_shapeCoord - vec2(norm_step_x1, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_px2 = getHeightFromSDF((v_shapeCoord + vec2(norm_step_x2, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_nx2 = getHeightFromSDF((v_shapeCoord - vec2(norm_step_x2, 0.0)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);

    // Denominators are distances in pixels
    float grad_x1 = (h_px1 - h_nx1) / (2.0 * norm_step_x1 * u_glassSize.x);
    float grad_x2 = (h_px2 - h_nx2) / (2.0 * norm_step_x2 * u_glassSize.x);
    float delta_x = mix(grad_x1, grad_x2, 0.5);

    // Calculate Y direction gradient
    float h_py1 = getHeightFromSDF((v_shapeCoord + vec2(0.0, norm_step_y1)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_ny1 = getHeightFromSDF((v_shapeCoord - vec2(0.0, norm_step_y1)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_py2 = getHeightFromSDF((v_shapeCoord + vec2(0.0, norm_step_y2)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    float h_ny2 = getHeightFromSDF((v_shapeCoord - vec2(0.0, norm_step_y2)) * u_glassSize, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);

    float grad_y1 = (h_py1 - h_ny1) / (2.0 * norm_step_y1 * u_glassSize.y);
    float grad_y2 = (h_py2 - h_ny2) / (2.0 * norm_step_y2 * u_glassSize.y);
    float delta_y = mix(grad_y1, grad_y2, 0.5);

    vec3 surfaceNormal3D = normalize(vec3(-delta_x * u_normalStrength, -delta_y * u_normalStrength, 1.0));

    if (u_showNormals == 1) {
        gl_FragColor = vec4(surfaceNormal3D * 0.5 + 0.5, 1.0); // Remap from [-1,1] to [0,1] for color
        return;
    }

    vec3 incidentLightDir = normalize(vec3(0.0, 0.0, -1.0));
    vec3 refractedIntoGlass = refract(incidentLightDir, surfaceNormal3D, 1.0 / u_ior);
    vec3 refractedOutOfGlass = refract(refractedIntoGlass, -surfaceNormal3D, u_ior);

    vec2 offset_in_pixels = refractedOutOfGlass.xy * u_glassThickness;
    vec2 offset = (offset_in_pixels / u_resolution) * u_displacementScale;

    vec2 refractedTexCoord = v_screenTexCoord + offset;
    refractedTexCoord = clamp(refractedTexCoord, 0.001, 0.999);

    // Frosted Glass Effect: Apply a 3x3 box blur to the refracted texture
    vec4 blurredColor = vec4(0.0);
    vec2 texelSize = 1.0 / u_resolution; // Size of one pixel in texture coordinates
    float blurPixelRadius = u_blurRadius;

    // Unrolled 3x3 blur samples
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0, -1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0,  0.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0,  0.0) * blurPixelRadius * texelSize); // Center sample
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0,  0.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2(-1.0,  1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 0.0,  1.0) * blurPixelRadius * texelSize);
    blurredColor += texture2D(u_backgroundTexture, refractedTexCoord + vec2( 1.0,  1.0) * blurPixelRadius * texelSize);

    blurredColor /= 9.0; // Divide by total number of samples (3x3 = 9)

    // Mix with an overlay color to make the glass stand out more
    // The height value here can be used as an alpha or blending factor if desired
    // For a subtle overlay, we can just mix with a fixed alpha
    float height_val = getHeightFromSDF(current_p_pixel, glass_half_size_pixel, actualCornerRadius, u_sminSmoothing, u_heightTransitionWidth);
    vec4 finalColor = mix(blurredColor, u_overlayColor, height_val * 0.15); // Adjust 0.15 for desired opacity

    // Apply highlight on top of the final color
    float highlight_dist = abs(dist_for_shape_boundary);
    // The highlight will be strongest at highlight_dist = 0.0 and fade out towards u_highlightWidth
    float highlight_alpha = 1.0 - smoothstep(0.0, u_highlightWidth, highlight_dist);
    highlight_alpha = max(0.0, highlight_alpha); // Ensure it's not negative

    // Directional highlight based on normal
    // We want highlight stronger when surfaceNormal3D.x and surfaceNormal3D.y have the same sign
    // This corresponds to normals pointing towards top-left (-x, -y) or bottom-right (+x, +y) edges
    float directionalFactor = (surfaceNormal3D.x * surfaceNormal3D.y + 1.0) * 0.5; // Scales from 0 to 1
    // You can add a boost to this factor if the highlight is too subtle
    // directionalFactor = pow(directionalFactor, 0.5); // Example: apply power for non-linear control

    float finalHighlightAlpha = highlight_alpha * directionalFactor;

    gl_FragColor = mix(finalColor, vec4(1.0, 1.0, 1.0, 1.0), finalHighlightAlpha);
}`; }
    get backgroundVsSource() { return `attribute vec2 a_position; varying vec2 v_texCoord; void main() { gl_Position = vec4(a_position, 0.0, 1.0); v_texCoord = (a_position + 1.0) / 2.0; }`; }
    get backgroundFsSource() { return `precision mediump float; uniform sampler2D u_backgroundTexture; varying vec2 v_texCoord; void main() { gl_FragColor = texture2D(u_backgroundTexture, v_texCoord); }`; }
}

class GlassObject {
    constructor(position, size, properties) {
        this.position = position;
        this.size = size;
        this.properties = properties;
        this.isSelected = false;
        this.htmlElement = null;
        this.isManaged = false;
    }

    updateFromElement() {
        if (!this.htmlElement || !this.isManaged) return;
        const rect = this.htmlElement.getBoundingClientRect();
        this.position.x = rect.left + rect.width / 2 + window.scrollX;
        this.position.y = rect.top + rect.height / 2 + window.scrollY;
        if (this.size.width !== rect.width || this.size.height !== rect.height) {
            this.size.width = rect.width;
            this.size.height = rect.height;
            if (this.properties.cornerRadius !== undefined && this.size.width > 0 && this.size.height > 0) {
                this.properties.cornerRadius = Math.min(this.properties.cornerRadius, Math.min(this.size.width, this.size.height) / 2.0);
            }
        }
    }

    isInside(x, y) {
        const halfW = this.size.width / 2;
        const halfH = this.size.height / 2;
        return x > this.position.x - halfW && x < this.position.x + halfW && y > this.position.y - halfH && y < this.position.y + halfH;
    }

    draw(gl, locations, buffer) {
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.enableVertexAttribArray(locations.position);
        gl.vertexAttribPointer(locations.position, 2, gl.FLOAT, false, 0, 0);

        gl.uniform2f(locations.mousePos, this.position.x, gl.canvas.height - this.position.y);
        gl.uniform2f(locations.glassSize, this.size.width, this.size.height);
        Object.keys(this.properties).forEach(key => {
            const uniformLocation = locations[key];
            if (uniformLocation) {
                const value = this.properties[key];
                if (typeof value === 'boolean') {
                    gl.uniform1i(uniformLocation, value ? 1 : 0);
                } else {
                    gl.uniform1f(uniformLocation, value);
                }
            }
        });
        gl.uniform4f(locations.overlayColor, 1.0, 1.0, 1.0, this.isSelected ? 0.8 : 0.0);

        gl.drawArrays(gl.TRIANGLES, 0, 6);
    }

    static createDefault() {
        const properties = { cornerRadius: 80, ior: 1.15, glassThickness: 25, normalStrength: 50, displacementScale: 1.0, heightTransitionWidth: 10.0, sminSmoothing: 20, showNormals: false, blurRadius: 3.0, highlightWidth: 1.5 };
        const position = { x: window.innerWidth / 2 + (Math.random() - 0.5) * 200, y: window.innerHeight / 2 + (Math.random() - 0.5) * 200 };
        const size = { width: 300 + (Math.random() - 0.5) * 100, height: 400 + (Math.random() - 0.5) * 100 };
        return new GlassObject(position, size, properties);
    }

    static createManaged(htmlElement) {
        const rect = htmlElement.getBoundingClientRect();
        const defaultManagedProps = { cornerRadius: 30, ior: 1.1, glassThickness: 15, normalStrength: 20, displacementScale: 1.0, heightTransitionWidth: 10.0, sminSmoothing: 10, showNormals: false, blurRadius: 4.0, highlightWidth: 1.0 };
        const properties = {};
        for (const key in defaultManagedProps) {
            const kebabKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
            let valueFromDataset;
            if (htmlElement.dataset[key] !== undefined) { valueFromDataset = htmlElement.dataset[key]; }
            else if (htmlElement.dataset[kebabKey] !== undefined) { valueFromDataset = htmlElement.dataset[kebabKey]; }

            if (valueFromDataset !== undefined) {
                if (typeof defaultManagedProps[key] === 'boolean') { properties[key] = valueFromDataset === 'true'; }
                else if (typeof defaultManagedProps[key] === 'number') { properties[key] = parseFloat(valueFromDataset); }
                else { properties[key] = valueFromDataset; }
            } else { properties[key] = defaultManagedProps[key]; }
        }

        const position = { x: rect.left + rect.width / 2 + window.scrollX, y: rect.top + rect.height / 2 + window.scrollY };
        const size = { width: rect.width, height: rect.height };
        const obj = new GlassObject(position, size, properties);
        obj.htmlElement = htmlElement;
        obj.isManaged = true;
        if (obj.properties.cornerRadius !== undefined && size.width > 0 && size.height > 0) {
           obj.properties.cornerRadius = Math.min(obj.properties.cornerRadius, Math.min(size.width, size.height) / 2.0);
        } else { obj.properties.cornerRadius = 0; }
        return obj;
    }
}

class UIManager {
    constructor(scene) {
        this.scene = scene;
        this.settingsPanel = document.getElementById('settings-panel');
        this.bindGlobalControls();
    }

    bindGlobalControls() {
        document.getElementById('addGlassBtn').addEventListener('click', () => this.scene.addGlass());
        document.getElementById('imageUpload').onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => this.scene.webgl.loadTexture(e.target.result, () => this.scene.render()); // Added rerender callback
                reader.readAsDataURL(file);
            }
        };
    }

    update() {
        const selected = this.scene.selectedGlass;
        if (!selected) {
            this.settingsPanel.innerHTML = '<p class="text-sm text-center text-white/60">No glass pane selected.</p>';
            return;
        }
        const isManagedByElement = selected.isManaged;
        const currentWidth = selected.size ? selected.size.width : 100;
        const currentHeight = selected.size ? selected.size.height : 100;
        const cornerRadiusMax = Math.max(0, Math.min(currentWidth, currentHeight) / 2); // Ensure max is not negative

        this.settingsPanel.innerHTML = `
            <div class="p-3 bg-black/10 rounded-lg space-y-3">
                <h3 class="font-semibold text-white/90 text-sm">Dimensions ${isManagedByElement ? '<span class="text-xs text-yellow-400">(Managed)</span>' : ''}</h3>
                ${this._createSlider('glassWidth', 'Width', 50, 1200, 1, currentWidth, isManagedByElement)}
                ${this._createSlider('glassHeight', 'Height', 50, 1200, 1, currentHeight, isManagedByElement)}
                ${this._createSlider('cornerRadius', 'Corner Radius', 0, cornerRadiusMax, 1, selected.properties.cornerRadius)}
            </div>
            <div class="p-3 bg-black/10 rounded-lg space-y-3">
                <h3 class="font-semibold text-white/90 text-sm">Optical</h3>
                ${this._createSlider('ior', 'IOR', 1.0, 2.5, 0.01, selected.properties.ior)}
                ${this._createSlider('blurRadius', 'Frost/Blur', 0, 10, 0.1, selected.properties.blurRadius)}
            </div>
            <div class="p-3 bg-black/10 rounded-lg space-y-3">
                <h3 class="font-semibold text-white/90 text-sm">Surface & Edge</h3>
                ${this._createSlider('glassThickness', 'Thickness', 0, 100, 1, selected.properties.glassThickness)}
                ${this._createSlider('highlightWidth', 'Edge Highlight', 0, 10, 0.1, selected.properties.highlightWidth)}
            </div>
            <div class="p-3 bg-black/10 rounded-lg space-y-3">
                <h3 class="font-semibold text-white/90 text-sm">Advanced/Debug</h3>
                ${this._createSlider('normalStrength', 'Normal Strength', 0, 200, 1, selected.properties.normalStrength)}
                ${this._createSlider('displacementScale', 'Displacement', 0, 5, 0.05, selected.properties.displacementScale)}
                ${this._createSlider('heightTransitionWidth', 'Edge Transition', 0.1, 50, 0.1, selected.properties.heightTransitionWidth)}
                ${this._createSlider('sminSmoothing', 'SDF Smoothing', 0, 100, 0.5, selected.properties.sminSmoothing)}
                ${this._createCheckbox('showNormals', 'Show Normals', selected.properties.showNormals)}
            </div>`;
        this._bindInstanceControls(selected);
    }

    _createSlider(id, label, min, max, step, value, disabled = false) {
        const disabledAttr = disabled ? 'disabled' : '';
        const commonInputClasses = "w-full h-2 bg-gray-300 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer";
        const enabledInputClasses = "hover:bg-gray-400 dark:hover:bg-gray-600";
        const disabledInputClasses = disabled ? "opacity-50 cursor-not-allowed" : enabledInputClasses;
        const labelClasses = disabled ? "text-gray-500 dark:text-gray-400" : "";
        const valueDisplayClasses = disabled ? "text-gray-500 dark:text-gray-400" : "";

        return `<div><div class="flex justify-between text-xs mb-1"><label class="${labelClasses}">${label}</label><span data-value-for="${id}" class="${valueDisplayClasses}">${parseFloat(value).toFixed(2)}</span></div><input type="range" data-control-id="${id}" min="${min}" max="${max}" step="${step}" value="${value}" class="${commonInputClasses} ${disabledInputClasses}" ${disabledAttr}></div>`;
    }

    _createCheckbox(id, label, value) {
        return `<div class="flex items-center justify-between pt-2"><label class="text-xs">${label}</label><input type="checkbox" data-control-id="${id}" class="w-4 h-4 rounded text-blue-500" ${value ? 'checked' : ''}></div>`;
    }

    _bindInstanceControls(instance) {
        this.settingsPanel.querySelectorAll('[data-control-id]').forEach(el => {
            const id = el.dataset.controlId;
            const isCheckbox = el.type === 'checkbox';

            el.addEventListener('input', (e) => {
                if (e.target.disabled) return;
                const value = isCheckbox ? e.target.checked : parseFloat(e.target.value);

                if (id === 'glassWidth' || id === 'glassHeight') {
                    if (!instance.isManaged) {
                        instance.size[id.replace('glass', '').toLowerCase()] = value;
                    }
                } else if (instance.properties.hasOwnProperty(id)) {
                    instance.properties[id] = value;
                }

                if (!isCheckbox) {
                   const valueDisplay = this.settingsPanel.querySelector(`[data-value-for="${id}"]`);
                   if (valueDisplay) valueDisplay.textContent = value.toFixed(2);
                }

                if (id === 'cornerRadius') {
                    const currentW = instance.size ? instance.size.width : Infinity;
                    const currentH = instance.size ? instance.size.height : Infinity;
                    instance.properties.cornerRadius = Math.min(value, Math.min(currentW, currentH) / 2);
                    if (!isCheckbox) {
                        const valueDisplay = this.settingsPanel.querySelector(`[data-value-for="${id}"]`);
                        if (valueDisplay) valueDisplay.textContent = instance.properties.cornerRadius.toFixed(2);
                        if (parseFloat(el.value) !== instance.properties.cornerRadius) {
                           el.value = instance.properties.cornerRadius;
                        }
                    }
                }
                this.scene.render();
            });
        });
    }
}

class GlassScene {
    constructor(canvasId) {
        this.webgl = new WebGLManager(canvasId);
        this.ui = new UIManager(this);
        this.canvas = this.webgl.canvas;

        this.glassObjects = [];
        this.selectedGlass = null;
        this.draggedGlass = null;
        this.dragOffset = { x: 0, y: 0 };

        this.init();
    }

    init() {
        this._bindCanvasEvents();
        this._discoverManagedObjects();
        if (document.getElementById('controls') && this.glassObjects.length === 0) {
            this.addGlass();
        } else if (this.glassObjects.length > 0) {
             this._selectGlass(this.glassObjects.find(g => g.isManaged) || this.glassObjects[0]);
        }
        // No automatic UI update here, _selectGlass calls render which should trigger UI update if needed.
        this.renderLoop = this.render.bind(this);
        requestAnimationFrame(this.renderLoop);
    }

    _discoverManagedObjects() {
        const targetElements = document.querySelectorAll('.glassmorphism-target');
        targetElements.forEach(el => {
            el.style.backgroundColor = 'transparent';
            const managedGlass = GlassObject.createManaged(el);
            this.glassObjects.push(managedGlass);
        });
    }

    addGlass() {
        const newGlass = GlassObject.createDefault();
        this.glassObjects.push(newGlass);
        this._selectGlass(newGlass);
        if(this.ui) this.ui.update(); // Update UI when new glass is added
        this.render(); // Request a render
    }

    render() {
        const { gl, canvas } = this.webgl;
        if (canvas.width !== window.innerWidth || canvas.height !== window.innerHeight) {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            gl.viewport(0, 0, canvas.width, canvas.height);
        }

        for (const glassObj of this.glassObjects) {
            if (glassObj.isManaged && glassObj.htmlElement) {
                glassObj.updateFromElement();
            }
        }
        this.webgl.drawBackground();

        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
        gl.useProgram(this.webgl.programs.glass);
        gl.uniform2f(this.webgl.locations.glass.resolution, canvas.width, canvas.height);
        gl.uniform1i(this.webgl.locations.glass.backgroundTexture, 0);

        for (const glassObj of this.glassObjects) {
            glassObj.draw(gl, this.webgl.locations.glass, this.webgl.buffers.quad);
        }
        gl.disable(gl.BLEND);

        // Only request next frame if loop is active (it is by default here)
        // requestAnimationFrame(this.renderLoop); // Moved to init to start loop
    }

    _selectGlass(glass) {
        if (this.selectedGlass) this.selectedGlass.isSelected = false;
        this.selectedGlass = glass;
        if (this.selectedGlass) this.selectedGlass.isSelected = true;
        if(this.ui) this.ui.update();
        this.render(); // Request a render
    }

    _bindCanvasEvents() {
        this.canvas.addEventListener('mousedown', (e) => {
            let foundGlass = null;
            for (let i = this.glassObjects.length - 1; i >= 0; i--) {
                if (this.glassObjects[i].isInside(e.clientX, e.clientY)) {
                    foundGlass = this.glassObjects[i];
                    break;
                }
            }
            if (foundGlass) {
                const index = this.glassObjects.indexOf(foundGlass);
                if (index !== -1 && index < this.glassObjects.length - 1) {
                    const item = this.glassObjects.splice(index, 1)[0];
                    this.glassObjects.push(item);
                }
                if (!foundGlass.isManaged) {
                    this.draggedGlass = foundGlass;
                    this.dragOffset = { x: e.clientX - foundGlass.position.x, y: e.clientY - foundGlass.position.y };
                } else {
                    this.draggedGlass = null;
                }
            }
            this._selectGlass(foundGlass); // This already calls this.render() and this.ui.update()
        });
        this.canvas.addEventListener('mousemove', (e) => {
            if (this.draggedGlass) {
                this.draggedGlass.position.x = e.clientX - this.dragOffset.x;
                this.draggedGlass.position.y = e.clientY - this.dragOffset.y;
                this.render(); // Request a render
            }
        });
        this.canvas.addEventListener('mouseup', () => { this.draggedGlass = null; });
        this.canvas.addEventListener('mouseleave', () => { this.draggedGlass = null; });
    }
}

// --- Application Entry Point ---
// This part will be handled by the Blade template loading logic
// try {
//     new GlassScene('glCanvas');
// } catch (error) {
//     console.error("Failed to initialize the Glass Scene:", error);
//     document.body.innerHTML = `<div style="position:fixed; top:0; left:0; width:100%; height:100%; display:flex; justify-content:center; align-items:center; color:white; font-size:1.5rem; padding: 2rem; text-align: center;">${error.message}</div>`;
// }

export { GlassScene, WebGLManager, GlassObject, UIManager };
