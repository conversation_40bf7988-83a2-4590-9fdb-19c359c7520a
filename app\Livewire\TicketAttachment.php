<?php

namespace App\Livewire;

use Livewire\Component;

class TicketAttachmentComponent extends Component
{
    public $attachment;
    public $galleryIndex = 0;

    public function mount($attachment, $galleryIndex = 0)
    {
        $this->attachment = $attachment;
        $this->galleryIndex = $galleryIndex;
    }

    public function render()
    {
        return view('livewire.ticket-attachment');
    }

    public function openImageGallery($messageId, $index)
    {
        $this->dispatch('open-image-gallery', messageId: $messageId, index: $index);
    }

    public function openVideoModal($url, $title)
    {
        $this->dispatch('open-video-modal', url: $url, title: $title);
    }

    public function openPdfModal($url, $title)
    {
        $this->dispatch('open-pdf-modal', url: $url, title: $title);
    }
}
