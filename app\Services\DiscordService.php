<?php

namespace App\Services;

use App\Models\Application;
use App\Models\Ticket;
use App\Models\TicketMessage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;

class DiscordService
{
    /**
     * Send a direct message to a Discord user
     *
     * @param string $userId Discord user ID
     * @param string $message Message content
     * @return array Response with status and details
     */
    public function sendDirectMessage(string $userId, string $message): array
    {
        try {
            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/messages/send";
            $apiKey = config('services.discord.api_key');

            if (empty($apiKey)) {
                Log::warning("Discord Bot API-Key nicht konfiguriert");
                return [
                    'success' => false,
                    'message' => 'Discord Bot API-Key nicht konfiguriert',
                    'error_type' => 'configuration_error',
                    'user_friendly' => 'Die Discord-Konfiguration ist unvollständig. Bitte kontaktieren Sie einen Administrator.'
                ];
            }

            Log::debug("Sende Discord-Nachricht an Benutzer", [
                'user_id' => $userId,
                'endpoint' => $endpoint,
                'message_length' => strlen($message)
            ]);

            // Enhanced retry logic with exponential backoff
            $maxRetries = 3;
            $attempt = 0;
            $baseDelay = 1; // seconds

            do {
                $attempt++;

                if ($attempt > 1) {
                    $delay = $baseDelay * pow(2, $attempt - 2); // exponential backoff
                    Log::info("Discord API retry attempt {$attempt}, waiting {$delay} seconds");
                    sleep($delay);
                }

                try {
                    $response = Http::withHeaders([
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                        'X-Request-Source' => 'DiscordService'
                    ])
                    ->timeout(10)
                    ->post($endpoint, [
                        'user_id' => $userId,
                        'content' => $message
                    ]);

                    if ($response->successful()) {
                        $data = $response->json();

                        Log::info("Discord-Nachricht erfolgreich gesendet", [
                            'user_id' => $userId,
                            'response' => $data
                        ]);

                        return [
                            'success' => true,
                            'message' => 'Nachricht erfolgreich gesendet',
                            'data' => $data
                        ];
                    }

                    // Check if the Discord client is not ready (status 503)
                    if ($response->status() === 503) {
                        $responseBody = $response->json();
                        $apiMessage = $responseBody['message'] ?? '';
                        if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                            Log::warning("Discord Bot API: Discord client not ready, will retry. Message: '{$apiMessage}'", [
                                'user_id' => $userId,
                                'attempt' => $attempt
                            ]);
                            // Wait longer for the client to be ready (10-20 seconds)
                            $waitTime = 10 + (($attempt - 1) * 5);
                            Log::warning("Waiting {$waitTime} seconds before retry.", [
                                'user_id' => $userId,
                                'attempt' => $attempt,
                                'max_retries' => $maxRetries
                            ]);
                            sleep($waitTime);
                            continue;
                        }
                    }

                    // Log error details and start another attempt
                    Log::warning("Fehler bei der Bot-Antwort, Versuch {$attempt} von {$maxRetries}", [
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'user_id' => $userId
                    ]);

                } catch (\Exception $botError) {
                    Log::warning("Ausnahme bei Bot-Kommunikation, Versuch {$attempt} von {$maxRetries}", [
                        'error' => $botError->getMessage(),
                        'user_id' => $userId
                    ]);
                }
            } while ($attempt < $maxRetries);

            // After all attempts still no success
            return [
                'success' => false,
                'message' => 'Nachricht konnte nicht gesendet werden nach mehreren Versuchen'
            ];

        } catch (\Exception $e) {
            Log::error("Kritischer Fehler bei der Discord-Nachrichtenübermittlung", [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Kritischer Fehler: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send application response to a Discord user
     *
     * @param Application $application The application
     * @param string $message Formatted message content
     * @param array $rolesToAssign Array of role IDs to assign
     * @return array Response with status and details
     */
    public function sendApplicationResponse(Application $application, string $message, array $rolesToAssign = []): array
    {
        if (empty($application->discord_id)) {
            Log::warning("Keine Discord-ID für Bewerbung vorhanden", [
                'application_id' => $application->id
            ]);

            return [
                'success' => false,
                'message' => 'Keine Discord-ID für Bewerbung vorhanden'
            ];
        }

        try {
            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/messages/send";
            $apiKey = config('services.discord.api_key');

            // Stelle sicher, dass die Discord ID als String übergeben wird
            $discordId = (string) $application->discord_id;

            // Send request to Discord bot with retry logic
            $maxRetries = config('services.discord.send_message_max_retries', 3); // Default to 3 if not configured
            $attempt = 0;
            $baseTimeout = config('services.discord.send_message_timeout', 10); // Default to 10s

            do {
                $attempt++;
                $currentTimeout = $baseTimeout + ($attempt > 1 ? ($attempt -1) * 5 : 0); // Increase timeout for retries

                if ($attempt > 1) {
                    // Enhanced exponential backoff with progressive wait time
                    $waitTime = pow(2, $attempt - 2); // Wait 1s, 2s, 4s for attempts 2, 3, 4
                    Log::info("Discord message send: Waiting {$waitTime}s before attempt {$attempt}", [
                        'application_id' => $application->id,
                        'user_id' => $discordId,
                    ]);
                    sleep($waitTime);
                }

                try {
                    Log::debug("Attempting to send Discord message", [
                        'application_id' => $application->id,
                        'user_id' => $discordId,
                        'attempt' => $attempt,
                        'max_retries' => $maxRetries,
                        'timeout' => $currentTimeout,
                        'roles_to_assign_count' => count($rolesToAssign)
                    ]);

                    $response = Http::withHeaders([
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                        'X-Request-Source' => 'DiscordService'
                    ])
                    ->timeout($currentTimeout)
                    ->post($endpoint, [
                        'user_id' => $discordId, // Explizit als String
                        'content' => $message,
                        'assign_roles' => array_map('strval', $rolesToAssign) // Auch Rollen-IDs als Strings
                    ]);

                    if ($response->successful()) {
                        $data = $response->json();
                        Log::info("Discord-Nachricht erfolgreich gesendet", [
                            'application_id' => $application->id,
                            'user_id' => $discordId,
                            'roles_assigned_count' => count($rolesToAssign),
                            'response' => $data,
                            'attempt' => $attempt
                        ]);
                        return [
                            'success' => true,
                            'message' => 'Nachricht erfolgreich gesendet',
                            'data' => $data
                        ];
                    }

                    // Enhanced handling for Discord client not ready (status 503)
                    if ($response->status() === 503 && $attempt < $maxRetries) {
                        $responseBody = $response->json();
                        $apiMessage = $responseBody['message'] ?? '';
                        if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                            Log::warning("Discord Bot API: Discord client not ready, will retry. Message: '{$apiMessage}'", [
                                'user_id' => $discordId,
                                'application_id' => $application->id,
                                'attempt' => $attempt
                            ]);
                            // Wait longer for the client to be ready (5 seconds)
                            sleep(5);
                            continue;
                        }
                    }

                    Log::warning("Fehler bei der Bot-Antwort, Versuch {$attempt} von {$maxRetries}", [
                        'application_id' => $application->id,
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'user_id' => $discordId,
                        'will_retry' => $attempt < $maxRetries
                    ]);

                    // Specific handling for 503 to ensure retry
                    if ($response->status() == 503 && $attempt < $maxRetries) {
                        continue; // Force retry if 503 and not max attempts
                    }

                } catch (\Illuminate\Http\Client\ConnectionException $ce) {
                    Log::warning("Verbindungsfehler beim Senden der Discord-Nachricht, Versuch {$attempt} von {$maxRetries}", [
                        'application_id' => $application->id,
                        'error' => $ce->getMessage(),
                        'user_id' => $discordId,
                        'will_retry' => $attempt < $maxRetries
                    ]);
                } catch (\Exception $botError) { // Catch other general exceptions during HTTP call
                    Log::warning("Ausnahme bei Bot-Kommunikation, Versuch {$attempt} von {$maxRetries}", [
                        'application_id' => $application->id,
                        'error' => $botError->getMessage(),
                        'user_id' => $discordId,
                        'will_retry' => $attempt < $maxRetries
                    ]);
                }
            } while ($attempt < $maxRetries);

            // After all attempts still no success
            Log::error("Discord-Nachricht konnte nicht gesendet werden nach {$maxRetries} Versuchen", [
                'application_id' => $application->id,
                'user_id' => $discordId,
                'last_status' => isset($response) ? $response->status() : 'N/A',
                'last_body' => isset($response) ? $response->body() : 'N/A'
            ]);
            return [
                'success' => false,
                'message' => "Nachricht konnte nicht gesendet werden nach {$maxRetries} Versuchen."
            ];

        } catch (\Exception $e) { // Catch exceptions outside the retry loop (e.g., config issues)
            Log::error("Kritischer Fehler bei der Discord-Nachrichtenübermittlung (ausserhalb der Retry-Logik)", [
                'application_id' => $application->id,
                'user_id' => $discordId ?? $application->discord_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Kritischer Fehler: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if the Discord bot is running
     *
     * @return bool True if the bot is running
     */
    public function isBotRunning(): bool
    {
        try {
            $botApiUrl = env('DISCORD_BOT_API_URL', 'http://localhost:' . env('DISCORD_BOT_PORT', '3001'));
            $endpoint = "{$botApiUrl}/api/ping";
            $apiKey = config('services.discord.api_key');

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->timeout(2)->get($endpoint);

            return $response->successful();
        } catch (\Exception $e) {
            Log::warning("Fehler bei der Überprüfung des Bot-Status", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get detailed bot status with enhanced health monitoring
     *
     * @return array Bot status information
     */
    public function getBotStatus(): array
    {
        // Try to get from cache first
        $cacheKey = 'discord_bot_status';
        $cachedStatus = Cache::get($cacheKey);

        if ($cachedStatus && $cachedStatus['last_check'] > now()->subMinutes(2)) {
            return $cachedStatus;
        }

        try {
            $botApiUrl = env('DISCORD_BOT_API_URL', 'http://localhost:' . env('DISCORD_BOT_PORT', '3001'));
            $endpoint = "{$botApiUrl}/api/status";
            $apiKey = config('services.discord.api_key');

            $startTime = microtime(true);

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->timeout(5)->get($endpoint);

            $responseTime = round((microtime(true) - $startTime) * 1000, 2); // ms

            if ($response->successful()) {
                $data = $response->json();

                $status = [
                    'online' => true,
                    'uptime' => $data['uptime'] ?? null,
                    'last_check' => now(),
                    'users_synced' => $data['users_synced'] ?? null,
                    'version' => $data['version'] ?? 'unknown',
                    'response_time_ms' => $responseTime,
                    'connection_quality' => $this->determineConnectionQuality($responseTime),
                    'memory_usage' => $data['memory_usage'] ?? null,
                    'active_connections' => $data['active_connections'] ?? null,
                    'last_error' => null,
                    'error' => null
                ];

                // Cache the status
                Cache::put($cacheKey, $status, now()->addMinutes(5));

                return $status;
            }

            $status = [
                'online' => false,
                'uptime' => null,
                'last_check' => now(),
                'users_synced' => null,
                'response_time_ms' => $responseTime ?? null,
                'connection_quality' => 'unavailable',
                'error' => 'API response error: ' . $response->status()
            ];

            // Cache the status
            Cache::put($cacheKey, $status, now()->addMinutes(1));

            return $status;
        } catch (\Exception $e) {
            $status = [
                'online' => false,
                'uptime' => null,
                'last_check' => now(),
                'users_synced' => null,
                'response_time_ms' => null,
                'connection_quality' => 'unavailable',
                'error' => 'Exception: ' . $e->getMessage()
            ];

            // Cache the status
            Cache::put($cacheKey, $status, now()->addMinutes(1));

            return $status;
        }
    }

    /**
     * Create a ticket channel on Discord
     *
     * @param Ticket $ticket The ticket to create a channel for
     * @return string|null The Discord channel ID if successful, null otherwise
     */
    public function createTicketChannel(Ticket $ticket): ?string
    {
        try {
            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/tickets/create-channel";
            $apiKey = config('services.discord.api_key');

            // Enhanced retry logic with exponential backoff
            $maxRetries = 3;
            $attempt = 0;
            $baseDelay = 1; // seconds

            do {
                $attempt++;

                if ($attempt > 1) {
                    $delay = $baseDelay * pow(2, $attempt - 2); // exponential backoff
                    Log::info("Discord channel creation retry attempt {$attempt} for ticket {$ticket->id}");
                    sleep($delay);
                }

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json',
                    'X-Request-Source' => 'DiscordService'
                ])
                ->timeout(10)
                ->post($endpoint, [
                    'ticket_id' => $ticket->id,
                    'user_id' => $ticket->user_id,
                    'title' => $ticket->title,
                    'description' => $ticket->description
                ]);

                if ($response->successful()) {
                    $data = $response->json();

                    Log::info("Discord ticket channel created successfully", [
                        'ticket_id' => $ticket->id,
                        'channel_id' => $data['channel_id'] ?? null
                    ]);

                    return $data['channel_id'] ?? null;
                }

                // Check if the Discord client is not ready (status 503)
                if ($response->status() === 503) {
                    $responseBody = $response->json();
                    $apiMessage = $responseBody['message'] ?? '';
                    if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                        if ($attempt < $maxRetries) {
                            // Wait longer for the client to be ready (10-20 seconds)
                            $waitTime = 10 + (($attempt - 1) * 5);
                            Log::warning("Discord Bot API: Discord client not ready for creating ticket channel, will retry. Message: '{$apiMessage}'. Waiting {$waitTime} seconds before retry.", [
                                'ticket_id' => $ticket->id,
                                'attempt' => $attempt,
                                'max_retries' => $maxRetries
                            ]);
                            sleep($waitTime);
                            continue;
                        }
                    }
                }

                Log::warning("Error creating Discord ticket channel", [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'ticket_id' => $ticket->id,
                    'attempt' => $attempt
                ]);

            } while ($attempt < $maxRetries && $response->status() >= 500);

            return null;
        } catch (\Exception $e) {
            Log::error("Critical error creating Discord ticket channel", [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Send a ticket message to Discord
     *
     * @param TicketMessage $message The message to send
     * @param int $retryCount Number of retries attempted (default: 0)
     * @param int $maxRetries Maximum number of retries (default: 3)
     * @return bool True if successful, false otherwise
     */
    public function sendTicketMessage(TicketMessage $message, int $retryCount = 0, int $maxRetries = 3): bool
    {
        try {
            $ticket = $message->ticket;

            if (empty($ticket->discord_channel_id)) {
                Log::warning("No Discord channel ID for ticket", [
                    'ticket_id' => $ticket->id
                ]);

                return false;
            }

            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/tickets/send-message";
            $apiKey = config('services.discord.api_key');

            // Prepare attachments data if any
            $attachments = [];
            foreach ($message->attachments as $attachment) {
                // Create a direct URL for the Discord bot to download the attachment
                // This uses API token authentication instead of URL signing
                $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
                $apiKey = config('services.discord.api_key');

                // Include the API token in the URL as a query parameter for simpler handling
                $botDownloadUrl = url("/api/tickets/attachments/{$attachment->id}/bot-download?api_token={$apiKey}");

                // Determine the filename to use - if the attachment has been processed, use a filename with the correct extension
                $filename = $attachment->original_filename;
                $contentType = $attachment->mime_type;

                // If the attachment has been processed, update the filename and content type
                if ($attachment->processing_status === 'completed' && $attachment->processed_file_path) {
                    $extension = pathinfo($attachment->processed_file_path, PATHINFO_EXTENSION);
                    $originalExtension = pathinfo($filename, PATHINFO_EXTENSION);

                    // If the extensions don't match, update the filename
                    if (strtolower($originalExtension) !== strtolower($extension) && !empty($extension)) {
                        $filename = pathinfo($filename, PATHINFO_FILENAME) . '.' . $extension;
                    }

                    // Update content type based on the processed file extension
                    if ($extension === 'mp4') {
                        $contentType = 'video/mp4';
                    } elseif ($extension === 'mp3') {
                        $contentType = 'audio/mpeg';
                    } elseif ($extension === 'jpg' || $extension === 'jpeg') {
                        $contentType = 'image/jpeg';
                    }
                }

                $attachments[] = [
                    'id' => $attachment->id,
                    'filename' => $filename,
                    'url' => $botDownloadUrl,
                    'size' => $attachment->file_size,
                    'content_type' => $contentType,
                    'processing_status' => $attachment->processing_status,
                    'is_processed' => $attachment->is_processed
                ];
            }

            // Increase timeout for each retry
            $timeout = 10 + ($retryCount * 5);

            // Log the request data for debugging
            Log::debug("Sending message to Discord", [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'channel_id' => $ticket->discord_channel_id,
                'user_id' => $message->user_id,
                'attachments_count' => count($attachments),
            ]);

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'X-Request-Source' => 'DiscordService'
            ])
            ->timeout($timeout)
            ->post($endpoint, [
                'channel_id' => $ticket->discord_channel_id,
                'user_id' => $message->user_id,
                'message' => $message->message,
                'is_from_discord' => false, // Explicitly set this flag to false for website messages
                'attachments' => $attachments
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Update the message with Discord message ID if provided
                if (!empty($data['message_id'])) {
                    $message->discord_message_id = $data['message_id'];
                    $message->save();
                }

                Log::info("Discord ticket message sent successfully", [
                    'ticket_id' => $ticket->id,
                    'message_id' => $message->id,
                    'discord_message_id' => $data['message_id'] ?? null,
                    'retries' => $retryCount
                ]);

                return true;
            }

            // Handle retry logic for certain status codes
            $status = $response->status();

            // Special handling for "Discord client not ready" error
            if ($status === 503) {
                $responseBody = $response->json();
                $apiMessage = $responseBody['message'] ?? '';
                if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                    if ($retryCount < $maxRetries) {
                        $nextRetry = $retryCount + 1;
                        // Wait longer for the client to be ready (10-20 seconds)
                        $waitTime = 10 + ($retryCount * 5);
                        Log::warning("Discord Bot API: Discord client not ready for sending ticket message, will retry. Message: '{$apiMessage}'. Waiting {$waitTime} seconds before retry.", [
                            'user_id' => $message->user_id,
                            'ticket_id' => $ticket->id,
                            'retry' => $nextRetry,
                            'max_retries' => $maxRetries
                        ]);

                        sleep($waitTime);
                        return $this->sendTicketMessage($message, $nextRetry, $maxRetries);
                    }
                }
            }
            // Standard retry for other server errors
            else if ($retryCount < $maxRetries && in_array($status, [408, 429, 500, 502, 504])) {
                Log::warning("Retrying Discord ticket message send after error", [
                    'status' => $status,
                    'retry' => $retryCount + 1,
                    'max_retries' => $maxRetries,
                    'ticket_id' => $ticket->id,
                    'message_id' => $message->id
                ]);

                // Exponential backoff: 1s, 2s, 4s
                $backoff = pow(2, $retryCount);
                sleep($backoff);

                return $this->sendTicketMessage($message, $retryCount + 1, $maxRetries);
            }

            Log::warning("Error sending Discord ticket message", [
                'status' => $status,
                'body' => $response->body(),
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'retries' => $retryCount
            ]);

            // Mark the message as failed to sync with Discord
            $message->sync_failed = true;
            $message->save();

            return false;
        } catch (\Exception $e) {
            Log::error("Critical error sending Discord ticket message", [
                'ticket_id' => $message->ticket_id,
                'message_id' => $message->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'retries' => $retryCount
            ]);

            // If we haven't exceeded max retries and it's a network-related exception, retry
            if ($retryCount < $maxRetries &&
                ($e instanceof \GuzzleHttp\Exception\ConnectException ||
                 $e instanceof \GuzzleHttp\Exception\ServerException)) {

                Log::warning("Retrying Discord ticket message send after exception", [
                    'exception' => get_class($e),
                    'retry' => $retryCount + 1,
                    'max_retries' => $maxRetries,
                    'ticket_id' => $message->ticket_id,
                    'message_id' => $message->id
                ]);

                // Exponential backoff: 1s, 2s, 4s
                $backoff = pow(2, $retryCount);
                sleep($backoff);

                return $this->sendTicketMessage($message, $retryCount + 1, $maxRetries);
            }

            // Mark the message as failed to sync with Discord
            $message->sync_failed = true;
            $message->save();

            return false;
        }
    }

    /**
     * Update ticket status on Discord
     *
     * @param Ticket $ticket The ticket to update
     * @return bool True if successful, false otherwise
     */
    public function updateTicketStatus(Ticket $ticket): bool
    {
        try {
            if (empty($ticket->discord_channel_id)) {
                Log::warning("No Discord channel ID for ticket", [
                    'ticket_id' => $ticket->id
                ]);

                return false;
            }

            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/tickets/update-status";
            $apiKey = config('services.discord.api_key');

            // Enhanced retry logic with exponential backoff
            $maxRetries = 3;
            $attempt = 0;
            $baseDelay = 1; // seconds

            do {
                $attempt++;

                if ($attempt > 1) {
                    $delay = $baseDelay * pow(2, $attempt - 2); // exponential backoff
                    Log::info("Discord status update retry attempt {$attempt} for ticket {$ticket->id}");
                    sleep($delay);
                }

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json',
                    'X-Request-Source' => 'DiscordService'
                ])
                ->timeout(10)
                ->post($endpoint, [
                    'channel_id' => $ticket->discord_channel_id,
                    'status' => $ticket->status
                ]);

                if ($response->successful()) {
                    Log::info("Discord ticket status updated successfully", [
                        'ticket_id' => $ticket->id,
                        'status' => $ticket->status
                    ]);

                    return true;
                }

                // Check if the Discord client is not ready (status 503)
                if ($response->status() === 503) {
                    $responseBody = $response->json();
                    $apiMessage = $responseBody['message'] ?? '';
                    if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                        if ($attempt < $maxRetries) {
                            // Wait longer for the client to be ready (10-20 seconds)
                            $waitTime = 10 + (($attempt - 1) * 5);
                            Log::warning("Discord Bot API: Discord client not ready for updating ticket status, will retry. Message: '{$apiMessage}'. Waiting {$waitTime} seconds before retry.", [
                                'ticket_id' => $ticket->id,
                                'attempt' => $attempt,
                                'max_retries' => $maxRetries
                            ]);
                            sleep($waitTime);
                            continue;
                        }
                    }
                }

                Log::warning("Error updating Discord ticket status", [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'ticket_id' => $ticket->id,
                    'attempt' => $attempt
                ]);

            } while ($attempt < $maxRetries && $response->status() >= 500);

            return false;
        } catch (\Exception $e) {
            Log::error("Critical error updating Discord ticket status", [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Update ticket assignment on Discord
     *
     * @param Ticket $ticket The ticket to update
     * @return bool True if successful, false otherwise
     */
    public function updateTicketAssignment(Ticket $ticket): bool
    {
        try {
            if (empty($ticket->discord_channel_id)) {
                Log::warning("No Discord channel ID for ticket", [
                    'ticket_id' => $ticket->id
                ]);

                return false;
            }

            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
            $endpoint = "{$botApiUrl}/api/tickets/update-assignment";
            $apiKey = config('services.discord.api_key');

            // Enhanced retry logic with exponential backoff
            $maxRetries = 3;
            $attempt = 0;
            $baseDelay = 1; // seconds

            do {
                $attempt++;

                if ($attempt > 1) {
                    $delay = $baseDelay * pow(2, $attempt - 2); // exponential backoff
                    Log::info("Discord assignment update retry attempt {$attempt} for ticket {$ticket->id}");
                    sleep($delay);
                }

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json',
                    'X-Request-Source' => 'DiscordService'
                ])
                ->timeout(10)
                ->post($endpoint, [
                    'channel_id' => $ticket->discord_channel_id,
                    'assigned_to' => $ticket->assigned_to
                ]);

                if ($response->successful()) {
                    Log::info("Discord ticket assignment updated successfully", [
                        'ticket_id' => $ticket->id,
                        'assigned_to' => $ticket->assigned_to
                    ]);

                    return true;
                }

                // Check if the Discord client is not ready (status 503)
                if ($response->status() === 503) {
                    $responseBody = $response->json();
                    $apiMessage = $responseBody['message'] ?? '';
                    if ($apiMessage === 'Discord client not ready' || $apiMessage === 'Discord client not fully ready') {
                        if ($attempt < $maxRetries) {
                            // Wait longer for the client to be ready (10-20 seconds)
                            $waitTime = 10 + (($attempt - 1) * 5);
                            Log::warning("Discord Bot API: Discord client not ready for updating ticket assignment, will retry. Message: '{$apiMessage}'. Waiting {$waitTime} seconds before retry.", [
                                'ticket_id' => $ticket->id,
                                'attempt' => $attempt,
                                'max_retries' => $maxRetries
                            ]);
                            sleep($waitTime);
                            continue;
                        }
                    }
                }

                Log::warning("Error updating Discord ticket assignment", [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'ticket_id' => $ticket->id,
                    'attempt' => $attempt
                ]);

            } while ($attempt < $maxRetries && $response->status() >= 500);

            return false;
        } catch (\Exception $e) {
            Log::error("Critical error updating Discord ticket assignment", [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Determine connection quality based on response time
     *
     * @param float $responseTimeMs Response time in milliseconds
     * @return string Connection quality rating
     */
    private function determineConnectionQuality(float $responseTimeMs): string
    {
        if ($responseTimeMs < 100) {
            return 'excellent';
        } elseif ($responseTimeMs < 300) {
            return 'good';
        } elseif ($responseTimeMs < 1000) {
            return 'fair';
        } else {
            return 'poor';
        }
    }
}
